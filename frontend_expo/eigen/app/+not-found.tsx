import { <PERSON>, Stack } from "expo-router";
import { View, Text, Pressable } from "react-native";

export default function NotFoundScreen() {
  return (
    <>
      <Stack.Screen options={{ title: "Oops!" }} />
      <View className="flex-1 items-center justify-center p-5">
        <Text className="text-2xl font-bold text-foreground">
          This screen does not exist.
        </Text>
        <Link href="/" asChild>
          <Pressable className="mt-4 py-4">
            <Text className="text-primary font-medium">Go to home test screen!</Text>
          </Pressable>
        </Link>
      </View>
    </>
  );
}
