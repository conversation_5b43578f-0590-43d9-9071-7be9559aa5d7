import React, { useState } from "react";
import { View, Text, ScrollView, TouchableOpacity, Alert } from "react-native";
import { z } from "zod";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Link, router } from "expo-router";
import { Eye, EyeOff, LogIn, Mail } from "lucide-react-native";
import { useAuth } from "../../contexts/AuthContext";
import { Button } from "../../components/ui/Button";
import { Input } from "../../components/ui/Input";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "../../components/ui/Card";

// Form validation schema
const passwordSigninSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
  password: z.string().min(1, "Password is required"),
});

const magicLinkSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
});

type PasswordSigninForm = z.infer<typeof passwordSigninSchema>;

export default function SignInScreen() {
  const { signIn, sendMagicLink } = useAuth();
  const [loading, setLoading] = useState(false);
  const [loadingMagicLink, setLoadingMagicLink] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [formError, setFormError] = useState<string | null>(null);

  const {
    control,
    handleSubmit,
    formState: { errors },
    getValues,
  } = useForm<PasswordSigninForm>({
    resolver: zodResolver(passwordSigninSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const onSubmit = async (data: PasswordSigninForm) => {
    setLoading(true);
    setFormError(null);
    setSuccess(null);

    try {
      const { error } = await signIn(data.email, data.password);

      if (error) {
        throw error;
      }

      // Navigate to the main app
      router.replace("/profile");
    } catch (error) {
      console.error("Sign in error:", error);
      setFormError(
        error instanceof Error ? error.message : "An unexpected error occurred"
      );
    } finally {
      setLoading(false);
    }
  };

  const handleMagicLinkSignIn = async () => {
    const email = getValues("email");

    try {
      // Validate email
      const validatedData = magicLinkSchema.parse({ email });

      setLoadingMagicLink(true);
      setFormError(null);
      setSuccess(null);

      const { error } = await sendMagicLink(validatedData.email);

      if (error) {
        throw error;
      }

      setSuccess("Magic link sent! Check your email inbox for a sign-in link.");
    } catch (error) {
      if (error instanceof z.ZodError) {
        setFormError("Please enter a valid email address");
      } else {
        setFormError(
          error instanceof Error ? error.message : "Failed to send magic link"
        );
      }
    } finally {
      setLoadingMagicLink(false);
    }
  };

  return (
    <ScrollView className="flex-1 bg-background">
      <View className="flex-1 p-6 justify-center">
        <Card className="w-full bg-card/80 border border-border/20 rounded-md">
          <CardHeader className="items-center pt-6 pb-4">
            <View className="mb-2">
              <LogIn size={32} color="#2c2c2e" />
            </View>
            <CardDescription className="text-center">
              Access your eigen dashboard.
            </CardDescription>
          </CardHeader>

          <CardContent className="pt-4 pb-6 px-6">
            {formError && (
              <View className="bg-destructive/10 border border-destructive/30 p-3 rounded-sm mb-4">
                <Text className="text-destructive text-xs">{formError}</Text>
              </View>
            )}

            {success && (
              <View className="bg-green-500/10 border border-green-500/30 p-3 rounded-sm mb-4">
                <Text className="text-green-700 text-xs">{success}</Text>
              </View>
            )}

            {/* Email Field (Shared) */}
            <Controller
              control={control}
              name="email"
              render={({ field: { onChange, onBlur, value } }) => (
                <Input
                  label="Email"
                  placeholder="<EMAIL>"
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoComplete="email"
                  autoCorrect={false}
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  error={errors.email?.message}
                  className="mb-4"
                />
              )}
            />

            {/* Password Field */}
            <Controller
              control={control}
              name="password"
              render={({ field: { onChange, onBlur, value } }) => (
                <View className="mb-4">
                  <View className="flex-row justify-between items-center mb-1.5">
                    <Text className="text-sm font-medium text-foreground">
                      Password
                    </Text>
                  </View>
                  <View className="relative flex-row items-center">
                    <Input
                      placeholder=">********"
                      secureTextEntry={!showPassword}
                      autoCapitalize="none"
                      autoComplete="password"
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      error={errors.password?.message}
                      className="pr-10 flex-1"
                      containerClassName="flex-1"
                    />
                    <TouchableOpacity
                      onPress={() => setShowPassword(!showPassword)}
                      className="absolute right-3"
                    >
                      {showPassword ? (
                        <EyeOff size={20} color="#9CA3AF" />
                      ) : (
                        <Eye size={20} color="#9CA3AF" />
                      )}
                    </TouchableOpacity>
                  </View>
                </View>
              )}
            />

            {/* Sign In Button */}
            <Button
              onPress={handleSubmit(onSubmit)}
              loading={loading}
              disabled={loading || loadingMagicLink}
              className="w-full bg-brand-black dark:bg-off-white mb-6"
              textClassName="text-off-white dark:text-brand-black"
            >
              Sign in with Password
            </Button>

            {/* Divider */}
            <View className="flex-row items-center mb-6">
              <View className="flex-1 h-px bg-border" />
              <Text className="mx-2 text-xs text-muted-foreground">or</Text>
              <View className="flex-1 h-px bg-border" />
            </View>

            {/* Magic Link Section */}
            <View className="items-center mb-4">
              <View className="flex-row items-center">
                <Mail size={16} color="#2c2c2e" />
                <Text className="ml-2 text-sm font-medium text-foreground">
                  Sign in with Magic Link
                </Text>
              </View>
              <Text className="text-xs text-muted-foreground text-center mt-2 max-w-xs">
                We'll send you a magic link to your email that will sign you in
                instantly.
              </Text>
            </View>

            {/* Magic Link Button */}
            <Button
              onPress={handleMagicLinkSignIn}
              loading={loadingMagicLink}
              disabled={loading || loadingMagicLink || !getValues("email")}
              variant="outline"
              className="w-full"
            >
              Sign in with Magic Link
            </Button>
          </CardContent>

          <CardFooter className="justify-center pb-6">
            <Text className="text-xs text-muted-foreground">
              Don't have an account?{" "}
              <Link href="/signup" asChild>
                <Text className="font-medium text-foreground underline">
                  Sign up
                </Text>
              </Link>
            </Text>
          </CardFooter>
        </Card>
      </View>
    </ScrollView>
  );
}
