import React, { useState } from "react";
import { View, Text, ScrollView, TouchableOpacity, Alert } from "react-native";
import { z } from "zod";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { <PERSON> } from "expo-router";
import { Eye, EyeOff, KeyRound } from "lucide-react-native";
import { useAuth } from "../../contexts/AuthContext";
import { Button } from "../../components/ui/Button";
import { Input } from "../../components/ui/Input";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "../../components/ui/Card";

// Form validation schema
const signupSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email address"),
  password: z.string().min(8, "Password must be at least 8 characters"),
});

type SignupForm = z.infer<typeof signupSchema>;

export default function SignUpScreen() {
  const { signUp } = useAuth();
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [formError, setFormError] = useState<string | null>(null);

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<SignupForm>({
    resolver: zodResolver(signupSchema),
    defaultValues: {
      name: "",
      email: "",
      password: "",
    },
  });

  const onSubmit = async (data: SignupForm) => {
    setLoading(true);
    setFormError(null);
    setSuccess(null);

    try {
      const { error } = await signUp(data.email, data.password, data.name);

      if (error) {
        throw error;
      }

      setSuccess(
        "Account created! Please check your email for a verification link. " +
          "You'll be able to sign in after verifying your email."
      );
    } catch (error) {
      console.error("Sign up error:", error);
      setFormError(
        error instanceof Error ? error.message : "An unexpected error occurred"
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <ScrollView className="flex-1 bg-background">
      <View className="flex-1 p-6 justify-center">
        <Card className="w-full bg-card/80 border border-border/20 rounded-md">
          <CardHeader className="items-center pt-6 pb-4">
            <View className="mb-2">
              <KeyRound size={32} color="#2c2c2e" />
            </View>
            <CardDescription className="text-center">
              Join eigen to compile your profile.
            </CardDescription>
          </CardHeader>

          <CardContent className="pt-4 pb-6 px-6">
            {formError && (
              <View className="bg-destructive/10 border border-destructive/30 p-3 rounded-sm mb-4">
                <Text className="text-destructive text-xs">{formError}</Text>
              </View>
            )}

            {success && (
              <View className="bg-green-500/10 border border-green-500/30 p-3 rounded-sm mb-4">
                <Text className="text-green-700 text-xs">{success}</Text>
              </View>
            )}

            {/* Name Field */}
            <Controller
              control={control}
              name="name"
              render={({ field: { onChange, onBlur, value } }) => (
                <Input
                  label="Name"
                  placeholder="Ada Lovelace"
                  autoCapitalize="words"
                  autoComplete="name"
                  autoCorrect={false}
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  error={errors.name?.message}
                  className="mb-4"
                />
              )}
            />

            {/* Email Field */}
            <Controller
              control={control}
              name="email"
              render={({ field: { onChange, onBlur, value } }) => (
                <Input
                  label="Email"
                  placeholder="<EMAIL>"
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoComplete="email"
                  autoCorrect={false}
                  value={value}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  error={errors.email?.message}
                  className="mb-4"
                />
              )}
            />

            {/* Password Field */}
            <Controller
              control={control}
              name="password"
              render={({ field: { onChange, onBlur, value } }) => (
                <View className="mb-4">
                  <View className="flex-row justify-between items-center mb-1.5">
                    <Text className="text-sm font-medium text-foreground">
                      Password
                    </Text>
                  </View>
                  <View className="relative flex-row items-center">
                    <Input
                      placeholder=">********"
                      secureTextEntry={!showPassword}
                      autoCapitalize="none"
                      autoComplete="password-new"
                      value={value}
                      onChangeText={onChange}
                      onBlur={onBlur}
                      error={errors.password?.message}
                      className="pr-10 flex-1"
                      containerClassName="flex-1"
                    />
                    <TouchableOpacity
                      onPress={() => setShowPassword(!showPassword)}
                      className="absolute right-3"
                    >
                      {showPassword ? (
                        <EyeOff size={20} color="#9CA3AF" />
                      ) : (
                        <Eye size={20} color="#9CA3AF" />
                      )}
                    </TouchableOpacity>
                  </View>
                </View>
              )}
            />

            {/* Sign Up Button */}
            <Button
              onPress={handleSubmit(onSubmit)}
              loading={loading}
              disabled={loading}
              className="w-full bg-brand-black dark:bg-off-white mb-6"
              textClassName="text-off-white dark:text-brand-black"
            >
              {loading ? "Compiling..." : "Sign up"}
            </Button>

            {/* Divider */}
            <View className="flex-row items-center mb-6">
              <View className="flex-1 h-px bg-border" />
              <Text className="mx-2 text-xs text-muted-foreground">
                Or continue with
              </Text>
              <View className="flex-1 h-px bg-border" />
            </View>

            {/* OAuth Providers (Placeholder) */}
            <Text className="text-xs text-muted-foreground text-center">
              (OAuth providers coming soon)
            </Text>
          </CardContent>

          <CardFooter className="justify-center pb-6">
            <Text className="text-xs text-muted-foreground">
              Already have an account?{" "}
              <Link href="/signin" asChild>
                <Text className="font-medium text-foreground underline">
                  Sign in
                </Text>
              </Link>
            </Text>
          </CardFooter>
        </Card>
      </View>
    </ScrollView>
  );
}
