import React from "react";
import { View, Text, SafeAreaView } from "react-native";
import { Stack } from "expo-router";
import { StatusBar } from "expo-status-bar";
import { useAuth } from "../../contexts/AuthContext";

export default function AppLayout() {
  const { user, loading } = useAuth();

  // If the user is not authenticated, redirect to the sign-in page
  if (!loading && !user) {
    return (
      <View className="flex-1 items-center justify-center bg-background">
        <Text className="text-foreground">
          Please sign in to access this page.
        </Text>
      </View>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-background">
      <StatusBar style="dark" />

      <Stack
        screenOptions={{
          headerShown: true,
          headerTitle: "eigen",
          headerTitleStyle: {
            fontWeight: "bold",
            fontFamily: "SpaceGrotesk_600SemiBold",
          },
          headerShadowVisible: false,
          contentStyle: { backgroundColor: "transparent" },
          headerStyle: {
            backgroundColor: "#f7f6f4", // background color
          },
          headerTintColor: "#2c2c2e", // foreground color
        }}
      />
    </SafeAreaView>
  );
}
