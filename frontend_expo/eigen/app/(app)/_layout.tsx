import React from "react";
import { View, Text } from "react-native";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { StatusBar } from "expo-status-bar";
import { useAuth } from "../../contexts/AuthContext";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { User, Activity } from "lucide-react-native";

// Import the screens
import RunningProfilesScreen from "./running-profiles/index";
import ProfileScreen from "./profile";

const Tab = createBottomTabNavigator();

export default function AppLayout() {
  const { user, loading } = useAuth();
  const insets = useSafeAreaInsets();

  // If the user is not authenticated, redirect to the sign-in page
  if (!loading && !user) {
    return (
      <View className="flex-1 items-center justify-center bg-background">
        <Text className="text-foreground">
          Please sign in to access this page.
        </Text>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-background" style={{ paddingTop: insets.top }}>
      <StatusBar style="dark" />

      <Tab.Navigator
        screenOptions={{
          headerShown: false, // Hide header completely
          tabBarStyle: {
            backgroundColor: "#f7f6f4",
            borderTopColor: "#e5e5e5",
            borderTopWidth: 1,
            paddingBottom: insets.bottom,
            paddingTop: 8,
            height: 60 + insets.bottom,
            position: "absolute",
            bottom: 0,
            left: 0,
            right: 0,
          },
          tabBarActiveTintColor: "#23453b", // primary color
          tabBarInactiveTintColor: "#6b7280",
          tabBarLabelStyle: {
            fontFamily: "SpaceGrotesk_500Medium",
            fontSize: 12,
            marginBottom: 4,
          },
        }}
      >
        <Tab.Screen
          name="Profiles"
          component={RunningProfilesScreen}
          options={{
            tabBarIcon: ({ color, size }) => (
              <Activity size={size} color={color} />
            ),
          }}
        />
        <Tab.Screen
          name="Settings"
          component={ProfileScreen}
          options={{
            tabBarIcon: ({ color, size }) => <User size={size} color={color} />,
          }}
        />
      </Tab.Navigator>
    </View>
  );
}
