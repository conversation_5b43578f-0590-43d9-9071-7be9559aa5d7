import React from "react";
import { View, Text } from "react-native";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { StatusBar } from "expo-status-bar";
import { useAuth } from "../../contexts/AuthContext";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { User, Activity } from "lucide-react-native";

// Import the screens
import RunningProfilesScreen from "./running-profiles/index";
import ProfileScreen from "./profile";

const Tab = createBottomTabNavigator();

export default function AppLayout() {
  const { user, loading } = useAuth();
  const insets = useSafeAreaInsets();

  // If the user is not authenticated, redirect to the sign-in page
  if (!loading && !user) {
    return (
      <View className="flex-1 items-center justify-center bg-background">
        <Text className="text-foreground">
          Please sign in to access this page.
        </Text>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-background" style={{ paddingTop: insets.top }}>
      <StatusBar style="dark" />

      <Tab.Navigator
        screenOptions={{
          headerShown: true,
          headerTitle: "eigen",
          headerTitleStyle: {
            fontWeight: "bold",
            fontFamily: "SpaceGrotesk_600SemiBold",
          },
          headerShadowVisible: false,
          headerStyle: {
            backgroundColor: "#f7f6f4", // background color
          },
          headerTintColor: "#2c2c2e", // foreground color
          tabBarStyle: {
            backgroundColor: "#f7f6f4",
            borderTopColor: "#e5e5e5",
            paddingBottom: insets.bottom,
            height: 60 + insets.bottom,
          },
          tabBarActiveTintColor: "#23453b", // primary color
          tabBarInactiveTintColor: "#6b7280",
          tabBarLabelStyle: {
            fontFamily: "SpaceGrotesk_500Medium",
            fontSize: 12,
          },
        }}
      >
        <Tab.Screen
          name="Profiles"
          component={RunningProfilesScreen}
          options={{
            tabBarIcon: ({ color, size }) => (
              <Activity size={size} color={color} />
            ),
          }}
        />
        <Tab.Screen
          name="Settings"
          component={ProfileScreen}
          options={{
            tabBarIcon: ({ color, size }) => <User size={size} color={color} />,
          }}
        />
      </Tab.Navigator>
    </View>
  );
}
