import "react-native-url-polyfill/auto";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { createClient } from "@supabase/supabase-js";
import Constants from "expo-constants";

// Use Expo Constants or fallback to hardcoded values for development
const supabaseUrl = Constants.expoConfig?.extra?.supabaseUrl;
const supabaseAnonKey = Constants.expoConfig?.extra?.supabaseAnonKey;

// Check if we have valid Supabase credentials
if (
  !supabaseUrl ||
  supabaseUrl.includes("your-supabase-project") ||
  !supabaseAnonKey ||
  supabaseAnonKey === "your-anon-key"
) {
  console.error(
    "Missing or invalid Supabase credentials. Please set SUPABASE_URL and SUPABASE_ANON_KEY in app.config.js or as environment variables."
  );
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
  global: {
    // Disable WebSockets completely to avoid Node.js dependencies
    fetch: fetch.bind(globalThis),
  },
  // Completely disable realtime subscriptions
  realtime: {
    // Set eventsPerSecond to 0 to disable realtime
    params: {
      eventsPerSecond: 0,
    },
  },
});
