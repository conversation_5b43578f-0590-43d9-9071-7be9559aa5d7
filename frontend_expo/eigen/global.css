@tailwind base;
@tailwind components;
@tailwind utilities;

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-shimmer {
  animation: shimmer 2.5s infinite;
}

@layer base {
  :root {
    --background: 0 0% 96.5%;
    /* HEX #f7f6f4 */
    --foreground: 240 1% 18%;
    /* HEX #2c2c2e */

    --card: 0 0% 96.5%;
    /* HEX #f7f6f4 */
    --card-foreground: 240 1% 18%;
    /* HEX #2c2c2e */

    --popover: 0 0% 96.5%;
    /* HEX #f7f6f4 */
    --popover-foreground: 240 1% 18%;
    /* HEX #2c2c2e */

    --primary: 163 34% 21%;
    /* HEX #23453b */
    --primary-foreground: 0 0% 96.5%;
    /* HEX #f7f6f4 */

    --secondary: 240 1% 90%;
    /* Lighter shade of black/grey for secondary elements */
    --secondary-foreground: 240 1% 18%;
    /* HEX #2c2c2e */

    --muted: 240 1% 90%;
    --muted-foreground: 240 1% 45%;
    /* Slightly lighter than foreground */

    --accent: 163 34% 21%;
    /* HEX #23453b */
    --accent-foreground: 0 0% 96.5%;
    /* HEX #f7f6f4 */

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 1% 85%;
    /* Lighter border */
    --input: 240 1% 85%;
    --ring: 163 34% 21%;
    /* HEX #23453b */

    --radius: 0.5rem;
  }

  .dark {
    --background: 240 1% 18%;
    /* HEX #2c2c2e */
    --foreground: 0 0% 96.5%;
    /* HEX #f7f6f4 */

    --card: 240 1% 18%;
    /* HEX #2c2c2e */
    --card-foreground: 0 0% 96.5%;
    /* HEX #f7f6f4 */

    --popover: 240 1% 18%;
    /* HEX #2c2c2e */
    --popover-foreground: 0 0% 96.5%;
    /* HEX #f7f6f4 */

    --primary: 163 34% 21%;
    /* HEX #23453b - keep same for dark, or make it slightly lighter if needed */
    --primary-foreground: 0 0% 96.5%;
    /* HEX #f7f6f4 */

    --secondary: 240 1% 25%;
    /* Slightly lighter than dark background */
    --secondary-foreground: 0 0% 96.5%;
    /* HEX #f7f6f4 */

    --muted: 240 1% 25%;
    --muted-foreground: 0 0% 70%;
    /* Lighter muted text for dark mode */

    --accent: 163 34% 21%;
    /* HEX #23453b */
    --accent-foreground: 0 0% 96.5%;
    /* HEX #f7f6f4 */

    --destructive: 0 70% 45%;
    /* Darker red for destructive actions in dark mode */
    --destructive-foreground: 0 0% 96.5%;
    /* HEX #f7f6f4 */

    --border: 240 1% 30%;
    /* Darker border for dark mode */
    --input: 240 1% 30%;
    --ring: 163 34% 21%;
    /* HEX #23453b */
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}
