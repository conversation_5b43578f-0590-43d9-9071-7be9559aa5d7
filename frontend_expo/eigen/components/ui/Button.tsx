import React from "react";
import { Pressable, Text, ActivityIndicator, View } from "react-native";

type ButtonVariant = "default" | "outline" | "ghost" | "link" | "destructive";
type ButtonSize = "default" | "sm" | "lg" | "icon";

interface ButtonProps {
  children: React.ReactNode;
  onPress?: () => void;
  variant?: ButtonVariant;
  size?: ButtonSize;
  disabled?: boolean;
  loading?: boolean;
  className?: string;
  textClassName?: string;
}

export function Button({
  children,
  onPress,
  variant = "default",
  size = "default",
  disabled = false,
  loading = false,
  className = "",
  textClassName = "",
}: ButtonProps) {
  let buttonClasses = "flex flex-row items-center justify-center rounded-md";
  let textClasses = "font-medium text-center";

  switch (variant) {
    case "default":
      buttonClasses += " bg-primary";
      textClasses += " text-primary-foreground";
      break;
    case "outline":
      buttonClasses += " border border-input bg-transparent";
      textClasses += " text-foreground";
      break;
    case "ghost":
      buttonClasses += " bg-transparent";
      textClasses += " text-foreground";
      break;
    case "link":
      buttonClasses += " bg-transparent p-0";
      textClasses += " text-primary underline";
      break;
    case "destructive":
      buttonClasses += " bg-destructive";
      textClasses += " text-destructive-foreground";
      break;
  }

  switch (size) {
    case "default":
      buttonClasses += " px-4 py-2";
      textClasses += " text-sm";
      break;
    case "sm":
      buttonClasses += " px-3 py-1";
      textClasses += " text-xs";
      break;
    case "lg":
      buttonClasses += " px-6 py-3";
      textClasses += " text-base";
      break;
    case "icon":
      buttonClasses += " p-2";
      break;
  }

  if (disabled || loading) {
    buttonClasses += " opacity-50";
  }

  buttonClasses += ` ${className}`;
  textClasses += ` ${textClassName}`;

  return (
    <Pressable
      onPress={onPress}
      disabled={disabled || loading}
      className={buttonClasses}
      style={({ pressed }) => [
        {
          opacity: pressed || disabled || loading ? 0.7 : 1,
        },
      ]}
    >
      {loading && (
        <View className="mr-2">
          <ActivityIndicator
            size="small"
            color={variant === "default" ? "#fff" : "#000"}
          />
        </View>
      )}
      {typeof children === "string" ? (
        <Text className={textClasses}>{children}</Text>
      ) : (
        children
      )}
    </Pressable>
  );
}
