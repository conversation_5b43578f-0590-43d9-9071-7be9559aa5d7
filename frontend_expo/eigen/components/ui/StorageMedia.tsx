import React, { useState, useEffect } from "react";
import { View, Text, Image, ActivityIndicator } from "react-native";
import { VideoView, useVideoPlayer } from "expo-video";
import { ImageIcon, Video as VideoIcon } from "lucide-react-native";

interface StorageMediaProps {
  storagePath: string | null | undefined;
  type: "image" | "video";
  label?: string;
  className?: string;
  aspectRatio?: "square" | "video" | "custom";
  showFakeDataLabel?: boolean;
  fallbackText?: string;
  loopPreview?: boolean;
  style?: any;
}

export function StorageMedia({
  storagePath,
  type,
  label,
  className = "",
  aspectRatio = "square",
  showFakeDataLabel = true,
  fallbackText = "No media available",
  loopPreview = false,
  style,
}: StorageMediaProps) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [signedUrl, setSignedUrl] = useState<string | null>(null);

  // Always call useVideoPlayer hook, but only use it for videos
  const player = useVideoPlayer(signedUrl || "", (player) => {
    if (type === "video" && loopPreview && signedUrl) {
      player.loop = true;
      player.muted = true;
      player.play();
    }
  });

  useEffect(() => {
    if (!storagePath) {
      setLoading(false);
      return;
    }

    // For now, we'll assume the storagePath is already a valid URL
    // In a real implementation, you would generate signed URLs here
    setSignedUrl(storagePath);
    setLoading(false);
  }, [storagePath]);

  const getAspectRatioStyle = () => {
    switch (aspectRatio) {
      case "square":
        return { aspectRatio: 1 };
      case "video":
        return { aspectRatio: 16 / 9 };
      case "custom":
        return { flex: 1 };
      default:
        return { aspectRatio: 1 };
    }
  };

  const isFakeData =
    storagePath?.includes("fake") || storagePath?.includes("placeholder");

  if (loading) {
    return (
      <View
        className={`bg-gray-100 rounded-lg items-center justify-center ${className}`}
        style={[getAspectRatioStyle(), style]}
      >
        <ActivityIndicator size="small" color="#6b7280" />
      </View>
    );
  }

  if (error || !signedUrl) {
    return (
      <View
        className={`bg-gray-100 rounded-lg items-center justify-center p-4 ${className}`}
        style={[getAspectRatioStyle(), style]}
      >
        {type === "image" ? (
          <ImageIcon size={32} color="#9ca3af" />
        ) : (
          <VideoIcon size={32} color="#9ca3af" />
        )}
        <Text className="text-xs text-gray-500 mt-2 text-center">
          {fallbackText}
        </Text>
      </View>
    );
  }

  if (type === "image") {
    return (
      <View className={`relative ${className}`} style={style}>
        <Image
          source={{ uri: signedUrl }}
          className="rounded-lg"
          style={[getAspectRatioStyle(), { width: "100%" }]}
          resizeMode="cover"
          onError={() => setError("Failed to load image")}
        />
        {showFakeDataLabel && isFakeData && (
          <View className="absolute bottom-0 left-0 right-0 bg-amber-500/70 rounded-b-lg">
            <Text className="text-white text-xs py-1 px-2 text-center">
              AI Generated
            </Text>
          </View>
        )}
        {label && <Text className="text-xs text-gray-600 mt-1">{label}</Text>}
      </View>
    );
  }

  // Video component
  return (
    <View className={`relative ${className}`} style={style}>
      <VideoView
        player={player}
        style={[getAspectRatioStyle(), { width: "100%" }]}
        contentFit="cover"
        allowsFullscreen={!loopPreview}
        showsTimecodes={!loopPreview}
      />
      {showFakeDataLabel && isFakeData && (
        <View className="absolute bottom-0 left-0 right-0 bg-amber-500/70 rounded-b-lg">
          <Text className="text-white text-xs py-1 px-2 text-center">
            AI Generated
          </Text>
        </View>
      )}
      {label && <Text className="text-xs text-gray-600 mt-1">{label}</Text>}
    </View>
  );
}
