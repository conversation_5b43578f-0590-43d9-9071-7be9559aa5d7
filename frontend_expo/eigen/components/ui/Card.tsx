import React from "react";
import { View, Text, Pressable } from "react-native";

interface CardProps {
  children: React.ReactNode;
  className?: string;
  onPress?: () => void;
}

export function Card({ children, className = "", onPress }: CardProps) {
  const Component = onPress ? Pressable : View;

  return (
    <Component
      onPress={onPress}
      className={`bg-card rounded-lg border border-border overflow-hidden ${className}`}
    >
      {children}
    </Component>
  );
}

interface CardHeaderProps {
  children: React.ReactNode;
  className?: string;
}

export function CardHeader({ children, className = "" }: CardHeaderProps) {
  return <View className={`p-6 ${className}`}>{children}</View>;
}

interface CardTitleProps {
  children: React.ReactNode;
  className?: string;
}

export function CardTitle({ children, className = "" }: CardTitleProps) {
  return (
    <Text className={`text-xl font-semibold text-card-foreground ${className}`}>
      {children}
    </Text>
  );
}

interface CardDescriptionProps {
  children: React.ReactNode;
  className?: string;
}

export function CardDescription({
  children,
  className = "",
}: CardDescriptionProps) {
  return (
    <Text className={`text-sm text-muted-foreground ${className}`}>
      {children}
    </Text>
  );
}

interface CardContentProps {
  children: React.ReactNode;
  className?: string;
}

export function CardContent({ children, className = "" }: CardContentProps) {
  return <View className={`p-6 pt-0 ${className}`}>{children}</View>;
}

interface CardFooterProps {
  children: React.ReactNode;
  className?: string;
}

export function CardFooter({ children, className = "" }: CardFooterProps) {
  return (
    <View className={`flex flex-row items-center p-6 pt-0 ${className}`}>
      {children}
    </View>
  );
}
