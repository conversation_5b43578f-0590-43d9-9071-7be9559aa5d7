# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
.next/
out/

# production
/build
/openpose/
/processed_videos/

# video and image folders
/backend/input_videos/
/backend/output_videos/
/backend/processed_videos/
/backend/input_images/
/backend/output_images/
/frontend/public/uploads/

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
.env*.local

# env files
.env
.env.local
.env.development
.env.production
.env.test

# venv
venv/

node_modules/
backend/openpose


# Temporary files
*.tmp
*.temp
.cache/
