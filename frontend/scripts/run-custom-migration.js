// Script to run a custom SQL migration directly
const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

async function runCustomMigration() {
  const prisma = new PrismaClient();
  
  try {
    console.log('Running custom migration to fix Recommendation model columns...');
    
    // Read the migration SQL file
    const migrationPath = path.join(__dirname, '../prisma/migrations/20240626_fix_recommendation_columns/migration.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // Execute the SQL directly
    await prisma.$executeRawUnsafe(migrationSQL);
    
    console.log('Migration completed successfully!');
  } catch (error) {
    console.error('Migration failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the migration
runCustomMigration();
