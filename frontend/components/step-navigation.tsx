"use client"

import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft, ArrowR<PERSON> } from "lucide-react"
import { useRunningProfile } from "@/app/contexts/RunningProfileContext"

type StepNavigationProps = {
  previousStep?: string
  nextStep?: string
  isNextDisabled?: boolean
}

export function StepNavigation({ 
  previousStep, 
  nextStep,
  isNextDisabled = false
}: StepNavigationProps) {
  const router = useRouter()
  const { steps, setCurrentStep } = useRunningProfile()
  
  const handlePrevious = () => {
    if (previousStep) {
      router.push(previousStep)
      
      // Update current step in context if it's one of our steps
      const prevStepIndex = steps.findIndex(step => step.href === previousStep)
      if (prevStepIndex !== -1) {
        setCurrentStep(prevStepIndex)
      }
    }
  }
  
  const handleNext = () => {
    if (nextStep) {
      router.push(nextStep)
      
      // Update current step in context if it's one of our steps
      const nextStepIndex = steps.findIndex(step => step.href === nextStep)
      if (nextStepIndex !== -1) {
        setCurrentStep(nextStepIndex)
      }
    }
  }
  
  return (
    <div className="flex justify-between mt-6 mb-4 sm:mt-8 sm:mb-0">
      {previousStep ? (
        <Button 
          variant="outline" 
          onClick={handlePrevious}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back
        </Button>
      ) : (
        <div></div> // Empty div for spacing
      )}
      
      {nextStep && (
        <Button 
          onClick={handleNext}
          disabled={isNextDisabled}
          className="flex items-center gap-2"
        >
          Continue
          <ArrowRight className="h-4 w-4" />
        </Button>
      )}
    </div>
  )
} 