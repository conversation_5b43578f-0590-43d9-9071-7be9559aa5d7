import React from "react";
import Image from "next/image"; // Assuming you might want an image later

export function FootPhotoGuidance() {
  // Replace with actual guidance image/text
  return (
    <div className="mb-4 p-4 border rounded-md bg-muted text-muted-foreground text-sm">
      <h4 className="font-semibold mb-2">How to take the photo:</h4>
      <ul className="list-disc pl-5 space-y-1">
        <li>Place your foot flat on a standard A4 paper.</li>
        <li>Ensure good lighting, avoiding shadows.</li>
        <li>Hold the camera directly above the foot.</li>
        <li>Make sure the entire foot and the paper edges are visible.</li>
        {/* Example placeholder for an image guide */}
        {/* <Image src="/path/to/guidance-image.png" alt="Foot photo example" width={200} height={150} className="mt-2 rounded" /> */}
      </ul>
    </div>
  );
}
