"use client";

import { RunningProfileStepper } from "./running-profile-stepper";
import { usePathname } from "next/navigation";
import {
  LayoutDashboard,
  Settings,
  BarChart3,
  Footprints,
  UserRound,
  PlusCircle,
} from "lucide-react";
import Link from "next/link";
import { cn } from "@/lib/utils";

// Interface for nav items
interface MobileNavItem {
  title: string;
  href: string; // Path without (userApp)
  icon: React.ComponentType<{ className?: string }>;
  matchExact?: boolean;
}

// Define main navigation links (matching sidebar)
const mainNavLinks: MobileNavItem[] = [
  {
    title: "Running Profiles",
    href: "/running-profiles",
    icon: LayoutDashboard,
    matchExact: true,
  },
  {
    title: "My Shoes",
    href: "/shoes",
    icon: Footprints,
  },
  {
    title: "Analytics",
    href: "/analytics",
    icon: BarChart3,
  },
  {
    title: "Settings",
    href: "/settings",
    icon: Settings,
  },
  // Add other links like Orders if needed
];

export function MobileNav({ onLinkClick }: { onLinkClick?: () => void }) {
  // Optional callback for closing sheet
  const pathname = usePathname() || "";

  // Check if we are on the multi-step profile creation page
  const isCreatingProfile = pathname === "/running-profile";

  // Active state function (consistent with sidebar)
  const isActive = (link: MobileNavItem) =>
    link.matchExact ? pathname === link.href : pathname?.startsWith(link.href);

  return (
    <div className="py-4 px-2 overflow-y-auto max-h-[calc(100vh-65px)] scrollbar-thin scrollbar-thumb-zinc-300 dark:scrollbar-thumb-sweetspot-black/50 scrollbar-track-transparent">
      {isCreatingProfile ? (
        // Show only the stepper when actively creating a profile
        <>
          <div className="flex items-center gap-2 mb-4 px-2">
            <Footprints className="h-5 w-5 text-brand-green" />
            <h3 className="text-base font-semibold font-sans text-zinc-900 dark:text-text-main">
              Running Profile
            </h3>
          </div>
          <div className="pb-10">
            <RunningProfileStepper />
          </div>
        </>
      ) : (
        // Show main navigation for other app pages
        <div className="space-y-4">
          {/* Main Menu Section */}
          <nav className="flex flex-col space-y-1">
            {mainNavLinks.map((link) => (
              <Link
                key={link.href}
                href={link.href}
                onClick={onLinkClick} // Close sheet on click
                className={cn(
                  "flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium transition-colors",
                  isActive(link)
                    ? "bg-brand-green/10 dark:bg-brand-green/20 text-brand-green dark:text-emerald-300 font-semibold"
                    : "text-zinc-700 dark:text-text-main/70 hover:bg-zinc-100/70 dark:hover:bg-sweetspot-black/70 hover:text-zinc-900 dark:hover:text-text-main"
                )}
              >
                <link.icon className="h-4 w-4" />
                <span>{link.title}</span>
              </Link>
            ))}
          </nav>

          {/* Separator */}
          <div className="border-t border-zinc-200 dark:border-text-main/15 mx-2"></div>

          {/* Create Profile Button/Link */}
          <div className="px-2">
            <Link
              href="/create-eigen-profile" // Link to the profile creation start page
              onClick={onLinkClick}
              className={cn(
                "flex items-center justify-center gap-2 px-3 py-2 text-sm font-medium rounded-md transition-colors w-full",
                "bg-brand-green/10 dark:bg-brand-green/20 text-brand-green dark:text-emerald-300 hover:bg-brand-green/20 dark:hover:bg-brand-green/30 border border-brand-green/30 dark:border-brand-green/40 font-semibold"
              )}
            >
              <PlusCircle className="h-4 w-4" />
              <span>Create New Profile</span>
            </Link>
          </div>
        </div>
      )}
    </div>
  );
}
