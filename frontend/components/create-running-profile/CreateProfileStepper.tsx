"use client"

import { motion } from "framer-motion"
import { usePathname } from "next/navigation"
import { useRunningProfile } from "@/app/contexts/RunningProfileContext"

export function CreateProfileStepper() {
  const pathname = usePathname()
  const { steps, currentStep } = useRunningProfile()

  // Don't show stepper on the start page
  if (pathname === "/create-eigen-profile") {
    return null
  }

  const currentStepInfo = steps[currentStep]
  const nextStepInfo = steps[currentStep + 1]

  // Circular progress calculation
  const radius = 28;
  const circumference = radius * 2 * Math.PI;
  const progress = ((currentStep + 1) / steps.length) * 100;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (progress / 100) * circumference;

  return (
    <div className="w-full mb-2 sm:mb-4">
      {/* Compact Horizontal Stepper */}
      <div className="flex items-center gap-3">
        {/* Circular Progress */}
        <div className="relative flex-shrink-0">
          <svg
            width={64}
            height={64}
            className="transform -rotate-90"
            viewBox="0 0 64 64"
          >
            {/* Background circle */}
            <circle
              cx={32}
              cy={32}
              r={radius}
              stroke="currentColor"
              strokeWidth={3}
              fill="transparent"
              className="text-muted/30"
            />
            {/* Progress circle */}
            <motion.circle
              cx={32}
              cy={32}
              r={radius}
              stroke="currentColor"
              strokeWidth={3}
              fill="transparent"
              strokeDasharray={strokeDasharray}
              strokeDashoffset={strokeDashoffset}
              strokeLinecap="round"
              className="text-primary transition-all duration-300 ease-in-out"
              initial={{ strokeDashoffset: circumference }}
              animate={{ strokeDashoffset }}
              transition={{ duration: 0.5, ease: "easeInOut" }}
            />
          </svg>
          {/* Step counter in center */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <div className="text-base font-semibold text-foreground">
                {currentStep + 1}
              </div>
              <div className="text-[10px] text-muted-foreground leading-none">
                of {steps.length}
              </div>
            </div>
          </div>
        </div>

        {/* Step Info - Aligned with circle center */}
        <div className="flex flex-col justify-center">
          <h2 className="text-lg font-semibold text-foreground leading-tight mb-0.5">
            {currentStepInfo?.title || "Step"}
          </h2>
          {nextStepInfo && (
            <p className="text-sm text-muted-foreground leading-tight">
              Next: {nextStepInfo.title}
            </p>
          )}
        </div>
      </div>
    </div>
  )
}
