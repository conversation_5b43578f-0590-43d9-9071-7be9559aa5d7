"use client";

import React, { useState, useEffect, useTransition } from "react";
import Image from "next/image";
import { useRunningProfile } from "@/app/contexts/RunningProfileContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useRouter } from "next/navigation";
import {
  Trash2,
  Loader2,
  Video,
  ArrowLeft,
  ArrowRight,
  Camera,
  Play,
} from "lucide-react";
import { uploadFile } from "@/lib/upload-helpers";
import { toast } from "sonner";
import { motion } from "framer-motion";

type MediaType = "image" | "video";
type FieldName =
  | "footImageTopLeftUrl"
  | "footImageTopRightUrl"
  | "footImageMedialLeftUrl"
  | "footImageMedialRightUrl"
  | "runningVideoPosteriorUrl"
  | "runningVideoSagittalUrl";

interface UnifiedUploadStepProps {
  title: string;
  fieldName: FieldName;
  mediaType: MediaType;
  nextStepHref: string;
  backStepHref: string;
  currentStepIndex: number;
}

// Content guidance for each upload type - Enhanced with more detailed instructions
const getGuidanceContent = (fieldName: FieldName, mediaType: MediaType) => {
  if (mediaType === "image") {
    switch (fieldName) {
      case "footImageTopLeftUrl":
        return "Place your left foot flat on a white sheet of paper. Take a photo from directly above, ensuring your entire foot is visible with clear toe and heel definition. Use good lighting and avoid shadows for optimal analysis.";
      case "footImageTopRightUrl":
        return "Place your right foot flat on a white sheet of paper. Take a photo from directly above, ensuring your entire foot is visible with clear toe and heel definition. Use good lighting and avoid shadows for optimal analysis.";
      case "footImageMedialLeftUrl":
        return "Stand naturally with your left foot's inner side facing the camera. Position yourself about 2 feet away to capture the full arch profile, ankle, and heel area. Ensure the foot is weight-bearing and clearly visible.";
      case "footImageMedialRightUrl":
        return "Stand naturally with your right foot's inner side facing the camera. Position yourself about 2 feet away to capture the full arch profile, ankle, and heel area. Ensure the foot is weight-bearing and clearly visible.";
      default:
        return "Take a clear, well-lit photo following the specific positioning instructions for accurate biomechanical analysis.";
    }
  } else {
    switch (fieldName) {
      case "runningVideoSagittalUrl":
        return "Record 10-15 seconds of yourself running at your normal comfortable pace from the side view. Ensure your full body is visible from head to toe, maintain steady camera position, and run naturally to capture your typical gait pattern.";
      case "runningVideoPosteriorUrl":
        return "Record 10-15 seconds of yourself running at your normal comfortable pace from directly behind. Focus on capturing your leg movement, foot strike pattern, and hip alignment. Keep the camera steady and ensure full body visibility.";
      default:
        return "Record a short video following the specific angle and duration instructions for comprehensive gait analysis.";
    }
  }
};



export const UnifiedUploadStep: React.FC<UnifiedUploadStepProps> = ({
  title,
  fieldName,
  mediaType,
  nextStepHref,
  backStepHref,
  currentStepIndex,
}) => {
  const { profileId, profileData, updateProfileData, setCurrentStep, steps } = useRunningProfile();
  const router = useRouter();
  const [isPending, startTransition] = useTransition();

  const [file, setFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  useEffect(() => {
    const existingUrl = profileData[fieldName] as string | null;
    if (existingUrl) {
      setPreviewUrl(existingUrl);
    }
  }, [profileData, fieldName]);

  useEffect(() => {
    if (file) {
      const objectUrl = URL.createObjectURL(file);
      setPreviewUrl(objectUrl);
      return () => URL.revokeObjectURL(objectUrl);
    } else if (!profileData[fieldName]) {
      setPreviewUrl(null);
    }
  }, [file, profileData, fieldName]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];

      // Basic validation
      if (mediaType === "image" && !selectedFile.type.startsWith("image/")) {
        setError("Please select an image file.");
        return;
      }
      if (mediaType === "video" && !selectedFile.type.startsWith("video/")) {
        setError("Please select a video file.");
        return;
      }

      setFile(selectedFile);
      setError(null);
    }
  };

  const handleRemove = () => {
    setFile(null);
    setError(null);
    setPreviewUrl(profileData[fieldName] as string | null);
  };

  const handleNext = () => {
    if (!file) {
      setError(`Please select ${mediaType === "image" ? "an image" : "a video"} to continue.`);
      return;
    }
    if (!profileId) {
      setError("Profile not found. Please try again.");
      return;
    }

    setError(null);
    setIsUploading(true);

    startTransition(async () => {
      try {
        const result = await uploadFile(file, profileId, fieldName);

        if (result.error) {
          throw new Error(result.error);
        }

        if (result.fileUrl) {
          updateProfileData({ [fieldName]: result.fileUrl });
          toast.success(`${mediaType === "image" ? "Image" : "Video"} uploaded successfully!`);
          setCurrentStep(currentStepIndex + 1);
          router.push(nextStepHref);
        }
      } catch (err) {
        const message = err instanceof Error ? err.message : "Upload failed";
        setError(message);
        toast.error(message);
      } finally {
        setIsUploading(false);
      }
    });
  };

  const handleBack = () => {
    setCurrentStep(currentStepIndex - 1);
    router.push(backStepHref);
  };

  const isLoading = isUploading || isPending;
  const accept = mediaType === "image" ? "image/*" : "video/*";
  const fileId = `${mediaType}Upload`;
  const guidanceText = getGuidanceContent(fieldName, mediaType);

  return (
    <div className="flex flex-col max-w-4xl mx-auto px-4 py-2">
      {/* Main Content */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="flex flex-col"
      >
        {/* Guidance Text */}
        <div className="text-center mb-2">
          <p className="text-sm text-muted-foreground max-w-2xl mx-auto leading-relaxed">
            {guidanceText}
          </p>
        </div>

        {/* Error Message */}
        {error && (
          <div className="p-2 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 border border-red-500/50 text-sm rounded-lg text-center mb-2">
            {error}
          </div>
        )}

        {/* Content Area - Two Column Layout on Desktop */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
          {/* Upload Controls */}
          <div className="flex flex-col justify-center space-y-2">
            <Input
              id={fileId}
              type="file"
              accept={accept}
              onChange={handleFileChange}
              className="hidden"
              disabled={isLoading}
            />

            <Label
              htmlFor={fileId}
              className={`flex items-center justify-center gap-2 cursor-pointer rounded-lg border-2 border-dashed border-muted-foreground/25 bg-muted/30 px-3 py-2.5 text-sm font-medium text-muted-foreground transition-colors hover:bg-muted/50 hover:border-muted-foreground/50 ${
                isLoading ? "opacity-50 cursor-not-allowed" : ""
              }`}
            >
              {mediaType === "image" ? (
                <Camera className="h-4 w-4" />
              ) : (
                <Play className="h-4 w-4" />
              )}
              <span className="text-sm">
                {file ? `Change ${mediaType}` : `Select ${mediaType}`}
              </span>
            </Label>

            {(file || profileData[fieldName]) && (
              <Button
                variant="outline"
                onClick={handleRemove}
                disabled={isLoading}
                className="flex items-center justify-center gap-2 text-destructive hover:text-destructive border-destructive/50 hover:border-destructive hover:bg-destructive/5 h-8"
                size="sm"
              >
                <Trash2 className="h-3 w-3" />
                <span className="text-xs">Remove</span>
              </Button>
            )}
          </div>

          {/* Preview Area */}
          <div className="border border-border rounded-lg p-2 bg-muted/20 flex items-center justify-center h-[160px] lg:h-[200px]">
            {previewUrl ? (
              <div className="relative w-full h-full max-h-[140px] lg:max-h-[180px]">
                {mediaType === "image" ? (
                  <Image
                    src={previewUrl}
                    alt="Preview"
                    fill
                    className="object-contain rounded-lg"
                    priority
                  />
                ) : (
                  <video
                    src={previewUrl}
                    controls
                    className="w-full h-full max-h-[140px] lg:max-h-[180px] object-contain rounded-lg"
                  >
                    Your browser does not support the video tag.
                  </video>
                )}
              </div>
            ) : (
              <div className="flex flex-col items-center text-center">
                {mediaType === "image" ? (
                  <Camera className="h-8 w-8 text-muted-foreground/40 mb-2" />
                ) : (
                  <Video className="h-8 w-8 text-muted-foreground/40 mb-2" />
                )}
                <p className="text-xs text-muted-foreground">
                  Preview will appear here
                </p>
              </div>
            )}
          </div>
        </div>
      </motion.div>

      {/* Navigation Buttons - Mobile-optimized spacing */}
      <div className="flex items-center justify-center pt-2 pb-4 gap-2">
        <Button
          variant="outline"
          onClick={handleBack}
          disabled={isLoading}
          className="flex items-center gap-2 flex-1 max-w-[240px] h-10"
        >
          <ArrowLeft className="h-4 w-4" />
          Back
        </Button>

        <Button
          onClick={handleNext}
          disabled={!file || isLoading}
          className="bg-primary text-primary-foreground hover:bg-primary/90 flex items-center gap-2 flex-1 max-w-[240px] h-10"
        >
          {isLoading ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin" />
              Uploading...
            </>
          ) : (
            <>
              Next
              <ArrowRight className="h-4 w-4" />
            </>
          )}
        </Button>
      </div>
    </div>
  );
};
