import React from "react";
import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Activity, Ruler, UserRound } from "lucide-react";
import { RunningProfile } from "@prisma/client";

interface ProfileSummaryProps {
  profile: RunningProfile;
}

export function ProfileSummary({ profile }: ProfileSummaryProps) {
  const renderDetailItem = (label: string, value: string | number | null | undefined) => {
    if (value === null || value === undefined || value === "") {
      return null;
    }
    
    return (
      <div className="py-2">
        <div className="flex justify-between">
          <span className="text-sm text-zinc-500 dark:text-zinc-400">{label}</span>
          <span className="text-sm font-medium text-zinc-900 dark:text-zinc-100">{value}</span>
        </div>
      </div>
    );
  };

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {/* Overview Card */}
      <Card className="bg-zinc-50 dark:bg-zinc-800/60 border border-zinc-200 dark:border-zinc-700 shadow-sm rounded-lg">
        <CardHeader className="pb-2 border-b border-zinc-200 dark:border-zinc-700">
          <CardTitle className="flex items-center gap-2 text-base font-medium text-zinc-900 dark:text-zinc-100">
            <UserRound className="h-4 w-4 text-zinc-500 dark:text-zinc-400" />
            Overview
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-3">
          <div className="divide-y divide-zinc-100 dark:divide-zinc-700/50">
            {renderDetailItem("Age", profile.age)}
            {renderDetailItem("Gender", profile.gender)}
            {renderDetailItem("Climate", profile.climate)}
            {renderDetailItem("Terrain", profile.terrain)}
          </div>
        </CardContent>
      </Card>

      {/* Athletic Data Card */}
      <Card className="bg-zinc-50 dark:bg-zinc-800/60 border border-zinc-200 dark:border-zinc-700 shadow-sm rounded-lg">
        <CardHeader className="pb-2 border-b border-zinc-200 dark:border-zinc-700">
          <CardTitle className="flex items-center gap-2 text-base font-medium text-zinc-900 dark:text-zinc-100">
            <Activity className="h-4 w-4 text-zinc-500 dark:text-zinc-400" />
            Running Data
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-3">
          <div className="divide-y divide-zinc-100 dark:divide-zinc-700/50">
            {renderDetailItem("Weekly Distance", profile.averageWeeklyKm)}
            {renderDetailItem("Average Pace", profile.averagePaceEasyLong)}
            {renderDetailItem("Cadence", profile.averageCadence)}
            {renderDetailItem("Previous Injuries", profile.previousInjuries)}
            {renderDetailItem("Injury Severity", profile.previousInjuriesSeverity)}
          </div>
        </CardContent>
      </Card>

      {/* Measurements Card */}
      <Card className="md:col-span-2 lg:col-span-1 bg-zinc-50 dark:bg-zinc-800/60 border border-zinc-200 dark:border-zinc-700 shadow-sm rounded-lg">
        <CardHeader className="pb-2 border-b border-zinc-200 dark:border-zinc-700">
          <CardTitle className="flex items-center gap-2 text-base font-medium text-zinc-900 dark:text-zinc-100">
            <Ruler className="h-4 w-4 text-zinc-500 dark:text-zinc-400" />
            Measurements
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-3">
          <div className="divide-y divide-zinc-100 dark:divide-zinc-700/50">
            {renderDetailItem("Height", profile.heightCm ? `${profile.heightCm} cm` : null)}
            {renderDetailItem("Weight", profile.weightKg ? `${profile.weightKg} kg` : null)}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
