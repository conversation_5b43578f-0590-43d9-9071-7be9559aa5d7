import React from "react";
import { RunningProfileData } from "@/app/contexts/RunningProfileContext";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  User,
  Ruler,
  Activity,
  Footprints,
  Video,
  Compass,
  Edit,
  RefreshCw,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { StorageMedia } from "@/components/ui/storage-media";

interface CollectedDataSummaryProps {
  profileData: RunningProfileData;
}

export function CollectedDataSummary({
  profileData,
}: CollectedDataSummaryProps) {
  const router = useRouter();
  // Only log profile data in development
  if (process.env.NODE_ENV === "development") {
    console.log("CollectedDataSummary received profileData:", profileData);
  }

  const handleRetakeFootImages = () => {
    router.push("/create-eigen-profile/foot-imaging/top-left");
  };

  const handleRetakeVideos = () => {
    router.push("/create-eigen-profile/running-videos/posterior");
  };

  const handleEditRunnerInfo = () => {
    router.push("/create-eigen-profile/runner-profile");
  };

  return (
    <div className="space-y-6">
      <Tabs defaultValue="runner-info" className="w-full">
        <TabsList className="grid grid-cols-3 mb-4">
          <TabsTrigger value="runner-info" className="flex items-center gap-1">
            <User className="h-4 w-4" /> Runner Info
          </TabsTrigger>
          <TabsTrigger value="foot-images" className="flex items-center gap-1">
            <Footprints className="h-4 w-4" /> Foot Images
          </TabsTrigger>
          <TabsTrigger
            value="running-videos"
            className="flex items-center gap-1"
          >
            <Video className="h-4 w-4" /> Running Videos
          </TabsTrigger>
        </TabsList>

        <TabsContent value="runner-info" className="space-y-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center gap-2">
                <User className="h-5 w-5 text-brand-green" /> Runner Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {/* Always show these fields, even if null */}
                <div className="bg-zinc-100/60 dark:bg-zinc-800/60 p-3 rounded-sm border border-zinc-200 dark:border-zinc-700">
                  <span className="text-xs text-zinc-500 dark:text-zinc-400 uppercase tracking-wider block mb-0.5">
                    Age
                  </span>
                  <span className="text-base font-medium">
                    {profileData.age
                      ? `${profileData.age} years`
                      : "Not specified"}
                  </span>
                </div>
                <div className="bg-zinc-100/60 dark:bg-zinc-800/60 p-3 rounded-sm border border-zinc-200 dark:border-zinc-700">
                  <span className="text-xs text-zinc-500 dark:text-zinc-400 uppercase tracking-wider block mb-0.5">
                    Height
                  </span>
                  <span className="text-base font-medium">
                    {profileData.heightCm
                      ? `${profileData.heightCm} cm`
                      : "Not specified"}
                  </span>
                </div>
                <div className="bg-zinc-100/60 dark:bg-zinc-800/60 p-3 rounded-sm border border-zinc-200 dark:border-zinc-700">
                  <span className="text-xs text-zinc-500 dark:text-zinc-400 uppercase tracking-wider block mb-0.5">
                    Weight
                  </span>
                  <span className="text-base font-medium">
                    {profileData.weightKg
                      ? `${profileData.weightKg} kg`
                      : "Not specified"}
                  </span>
                </div>
                <div className="bg-zinc-100/60 dark:bg-zinc-800/60 p-3 rounded-sm border border-zinc-200 dark:border-zinc-700">
                  <span className="text-xs text-zinc-500 dark:text-zinc-400 uppercase tracking-wider block mb-0.5">
                    Gender
                  </span>
                  <span className="text-base font-medium">
                    {profileData.gender || "Not specified"}
                  </span>
                </div>
                <div className="bg-zinc-100/60 dark:bg-zinc-800/60 p-3 rounded-sm border border-zinc-200 dark:border-zinc-700">
                  <span className="text-xs text-zinc-500 dark:text-zinc-400 uppercase tracking-wider block mb-0.5">
                    Running Goal
                  </span>
                  <span className="text-base font-medium">
                    {profileData.runningGoal || "Not specified"}
                  </span>
                </div>
              </div>

              <div className="space-y-3">
                <h3 className="text-sm font-medium text-brand-green">
                  Running Environment
                </h3>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  <div className="bg-zinc-100/60 dark:bg-zinc-800/60 p-3 rounded-sm border border-zinc-200 dark:border-zinc-700">
                    <span className="text-xs text-zinc-500 dark:text-zinc-400 uppercase tracking-wider block mb-0.5">
                      Terrain
                    </span>
                    <span className="text-base font-medium">
                      {profileData.terrain || "Not specified"}
                    </span>
                  </div>
                  <div className="bg-zinc-100/60 dark:bg-zinc-800/60 p-3 rounded-sm border border-zinc-200 dark:border-zinc-700">
                    <span className="text-xs text-zinc-500 dark:text-zinc-400 uppercase tracking-wider block mb-0.5">
                      Climate
                    </span>
                    <span className="text-base font-medium">
                      {profileData.climate || "Not specified"}
                    </span>
                  </div>
                  <div className="bg-zinc-100/60 dark:bg-zinc-800/60 p-3 rounded-sm border border-zinc-200 dark:border-zinc-700">
                    <span className="text-xs text-zinc-500 dark:text-zinc-400 uppercase tracking-wider block mb-0.5">
                      Weekly Distance
                    </span>
                    <span className="text-base font-medium">
                      {profileData.averageWeeklyKm || "Not specified"}
                    </span>
                  </div>
                </div>
              </div>

              <div className="flex justify-end">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleEditRunnerInfo}
                  className="flex items-center gap-1"
                >
                  <Edit className="h-4 w-4" /> Edit Runner Info
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="foot-images" className="space-y-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center gap-2">
                <Footprints className="h-5 w-5 text-brand-green" /> Foot Images
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <StorageMedia
                    storagePath={profileData.footImageTopLeftUrl}
                    type="image"
                    label="Top View - Left"
                    className="max-w-[150px] max-h-[150px] mx-auto"
                  />
                </div>
                <div>
                  <StorageMedia
                    storagePath={profileData.footImageTopRightUrl}
                    type="image"
                    label="Top View - Right"
                    className="max-w-[150px] max-h-[150px] mx-auto"
                  />
                </div>
                <div>
                  <StorageMedia
                    storagePath={profileData.footImageMedialLeftUrl}
                    type="image"
                    label="Medial View - Left"
                    className="max-w-[150px] max-h-[150px] mx-auto"
                  />
                </div>
                <div>
                  <StorageMedia
                    storagePath={profileData.footImageMedialRightUrl}
                    type="image"
                    label="Medial View - Right"
                    className="max-w-[150px] max-h-[150px] mx-auto"
                  />
                </div>
              </div>

              <div className="flex justify-end">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRetakeFootImages}
                  className="flex items-center gap-1"
                >
                  <RefreshCw className="h-4 w-4" /> Retake Foot Images
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="running-videos" className="space-y-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center gap-2">
                <Video className="h-5 w-5 text-brand-green" /> Running Videos
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <StorageMedia
                    storagePath={profileData.runningVideoPosteriorUrl}
                    type="video"
                    label="Posterior View"
                    aspectRatio="video"
                  />
                </div>
                <div>
                  <StorageMedia
                    storagePath={profileData.runningVideoSagittalUrl}
                    type="video"
                    label="Sagittal View"
                    aspectRatio="video"
                  />
                </div>
              </div>

              <div className="flex justify-end">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRetakeVideos}
                  className="flex items-center gap-1"
                >
                  <RefreshCw className="h-4 w-4" /> Retake Videos
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
