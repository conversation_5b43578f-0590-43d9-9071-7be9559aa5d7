"use client";

import React from "react";
import Link from "next/link";
import { RunningProfile, ShoeRecommendation } from "@/lib/types";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Award,
  Sparkles,
  ExternalLink,
  Info,
} from "lucide-react";
import { StorageMedia } from "@/components/ui/storage-media";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface RecommendationsSectionProps {
  profile: RunningProfile;
  recommendation?: ShoeRecommendation;
}

const RecommendationCard: React.FC<{
  shoeModel: any;
  explanation: string;
  position: number;
}> = ({ shoeModel, explanation, position }) => {
  if (!shoeModel) return null;
  
  const shoeImage =
    shoeModel.imageURLs && shoeModel.imageURLs.length > 0
      ? shoeModel.imageURLs[0]
      : null;

  return (
    <Card className="bg-background hover:bg-muted/30 transition-colors duration-150 border border-border hover:border-primary/50 shadow-sm flex flex-col h-full">
      <CardHeader className="p-3">
        <div className="flex items-start justify-between">
          <div>
            <CardTitle className="text-md font-semibold text-primary">
              {shoeModel.name || "Recommended Shoe"}
            </CardTitle>
            {shoeModel.brand && (
              <p className="text-xs text-muted-foreground">{shoeModel.brand}</p>
            )}
          </div>
          <Tooltip delayDuration={100}>
            <TooltipTrigger asChild>
              <Badge
                variant="outline"
                className="border-primary/50 text-primary ml-2 whitespace-nowrap cursor-help bg-green-500/10 text-green-700 dark:text-green-400 border-green-500/50"
              >
                <Sparkles className="mr-1 h-3 w-3" /> #{position}
              </Badge>
            </TooltipTrigger>
            <TooltipContent
              side="top"
              className="max-w-xs bg-popover text-popover-foreground border-border shadow-lg p-2 text-xs"
            >
              <p>
                Recommendation rank #{position}
              </p>
            </TooltipContent>
          </Tooltip>
        </div>
      </CardHeader>
      <CardContent className="p-3 pt-0 flex-grow">
        {shoeImage && (
          <div className="aspect-video w-full bg-muted rounded-md overflow-hidden mb-2 border border-border">
            <StorageMedia
              storagePath={shoeImage}
              type="image"
              aspectRatio="custom"
              className="object-contain w-full h-full"
            />
          </div>
        )}
        {explanation && (
          <div className="mb-2">
            <h4 className="text-xs font-semibold text-muted-foreground mb-0.5 flex items-center">
              <Tooltip delayDuration={100}>
                <TooltipTrigger asChild>
                  <Info size={12} className="mr-1 cursor-help" />
                </TooltipTrigger>
                <TooltipContent
                  side="top"
                  className="max-w-xs bg-popover text-popover-foreground border-border shadow-lg p-2 text-xs"
                >
                  <p>
                    Explanation of why this shoe is a good match, considering
                    your running style, foot characteristics, and goals.
                  </p>
                </TooltipContent>
              </Tooltip>
              Why this shoe:
            </h4>
            <p className="text-xs text-foreground/80 text-justify hyphens-auto leading-snug line-clamp-3 group-hover:line-clamp-none transition-all">
              {explanation}
            </p>
          </div>
        )}
      </CardContent>
      {shoeModel.purchaseUrl && (
        <CardFooter className="p-3 pt-1.5 mt-auto">
          <Button
            variant="outline"
            size="sm"
            asChild
            className="w-full border-primary/50 text-primary hover:bg-primary/10 hover:text-primary"
          >
            <a
              href={shoeModel.purchaseUrl}
              target="_blank"
              rel="noopener noreferrer"
            >
              View Retail Options <ExternalLink className="ml-1.5 h-3 w-3" />
            </a>
          </Button>
        </CardFooter>
      )}
    </Card>
  );
};

export function RecommendationsSection({
  profile,
  recommendation,
}: RecommendationsSectionProps) {
  const hasRecommendations = !!recommendation && (
    recommendation.shoeModel1 || 
    recommendation.shoeModel2 || 
    recommendation.shoeModel3 || 
    recommendation.shoeModel4 || 
    recommendation.shoeModel5
  );
  
  const defaultValue = hasRecommendations ? "recommendations" : undefined;
  
  // Create an array of shoe models and explanations
  const recommendedShoes = [];
  if (recommendation?.shoeModel1) {
    recommendedShoes.push({
      shoeModel: recommendation.shoeModel1,
      explanation: recommendation.explanation1 || "",
      position: 1
    });
  }
  if (recommendation?.shoeModel2) {
    recommendedShoes.push({
      shoeModel: recommendation.shoeModel2,
      explanation: recommendation.explanation2 || "",
      position: 2
    });
  }
  if (recommendation?.shoeModel3) {
    recommendedShoes.push({
      shoeModel: recommendation.shoeModel3,
      explanation: recommendation.explanation3 || "",
      position: 3
    });
  }

  return (
    <TooltipProvider>
      <Accordion
        type="single"
        collapsible
        defaultValue={defaultValue}
        className="w-full"
      >
        <AccordionItem value="recommendations" className="border-none">
          <AccordionTrigger className="flex items-center justify-between w-full p-3 bg-card hover:bg-muted/50 rounded-lg shadow-sm transition-colors duration-150 group hover:no-underline focus-visible:no-underline">
            <div className="flex items-center space-x-2.5">
              <Award className="h-5 w-5 text-primary" />
              <h3 className="text-base font-semibold text-foreground group-hover:text-primary transition-colors duration-150 no-underline group-hover:no-underline">
                {hasRecommendations
                  ? "Top Shoe Recommendations"
                  : "Shoe Recommendations"}
              </h3>
            </div>
          </AccordionTrigger>
          <AccordionContent className="pt-0 px-0.5 pb-0.5">
            <div className="border-t border-border dark:border-zinc-700/70 mt-1.5 mb-0.5"></div>
            <Card className="bg-transparent border-none shadow-none">
              <CardContent className="p-2 space-y-3">
                {!hasRecommendations ? (
                  <p className="text-xs text-muted-foreground text-center py-3">
                    No specific shoe recommendations available for this profile
                    yet. Further analysis or more profile data may be required.
                  </p>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-3">
                    {recommendedShoes.map((rec, index) => (
                      <RecommendationCard 
                        key={index} 
                        shoeModel={rec.shoeModel} 
                        explanation={rec.explanation}
                        position={rec.position}
                      />
                    ))}
                  </div>
                )}
                {hasRecommendations && (
                  <div className="mt-4 flex justify-center">
                    <Button
                      variant="outline"
                      size="sm"
                      asChild
                      className="border-primary/70 text-primary hover:bg-primary/10 text-xs px-3 py-1.5"
                    >
                      <Link
                        href={`/running-profiles/${profile.id}/recommendations`}
                      >
                        View All Recommendations
                      </Link>
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </TooltipProvider>
  );
}
