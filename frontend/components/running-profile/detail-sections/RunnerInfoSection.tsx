"use client";

import React from "react";
import { RunningProfile } from "@/lib/types";
import { format, differenceInYears } from "date-fns";
import {
  UserCircle,
  CalendarDays,
  Target,
  Edit3,
  Weight,
  Ruler,
  Gauge,
  Info,
  ShieldAlert,
  HelpCircle,
} from "lucide-react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Card, CardContent } from "@/components/ui/card";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface RunnerInfoSectionProps {
  profile: RunningProfile;
}

const DataItem: React.FC<{
  label: string;
  value: string | number | null | undefined;
  icon?: React.ElementType;
  unit?: string;
  explanation?: string;
}> = ({ label, value, icon: Icon, unit, explanation }) => (
  <div className="flex items-start space-x-2 p-2.5 bg-background hover:bg-muted/50 rounded-md transition-colors duration-150 min-h-[60px]">
    {Icon && <Icon className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />}
    <div className="flex-grow">
      <div className="flex items-center space-x-1">
        <p className="text-xs font-medium text-muted-foreground">{label}</p>
        {explanation && (
          <TooltipProvider delayDuration={100}>
            <Tooltip>
              <TooltipTrigger asChild>
                <HelpCircle className="h-3.5 w-3.5 text-muted-foreground/70 hover:text-primary cursor-help" />
              </TooltipTrigger>
              <TooltipContent
                side="top"
                align="center"
                className="max-w-xs text-xs bg-foreground text-background p-2"
              >
                <p>{explanation}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>
      <p className="text-sm font-semibold text-foreground break-words">
        {value ? `${value}${unit || ""}` : "N/A"}
      </p>
    </div>
  </div>
);

export function RunnerInfoSection({ profile }: RunnerInfoSectionProps) {
  const age = profile.age || null;

  return (
    <Accordion
      type="single"
      collapsible
      defaultValue="runner-info"
      className="w-full"
    >
      <AccordionItem value="runner-info" className="border-none">
        <AccordionTrigger className="flex items-center justify-between w-full p-3 bg-card hover:bg-muted/50 rounded-lg shadow-sm transition-colors duration-150 group hover:no-underline focus-visible:no-underline">
          <div className="flex items-center space-x-3">
            <UserCircle className="h-5 w-5 text-primary" />
            <h3 className="text-md font-semibold text-foreground group-hover:text-primary transition-colors duration-150 no-underline group-hover:no-underline">
              Runner Information
            </h3>
          </div>
        </AccordionTrigger>
        <AccordionContent className="pt-0 px-0.5 pb-0.5">
          <div className="border-t border-border dark:border-zinc-700/70 mt-1.5 mb-0.5"></div>
          <Card className="bg-transparent border-none shadow-none rounded-none">
            <CardContent className="p-3 space-y-2.5">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-2.5">
                <DataItem
                  label="Profile Name"
                  value={profile.name}
                  icon={UserCircle}
                />
                <DataItem
                  label="Age"
                  value={age !== null ? String(age) : null}
                  unit=" years"
                  icon={CalendarDays}
                />
                <DataItem
                  label="Sex/Gender"
                  value={profile.gender}
                  icon={UserCircle}
                />
                <DataItem
                  label="Weight"
                  value={profile.weightKg}
                  unit=" kg"
                  icon={Weight}
                />
                <DataItem
                  label="Height"
                  value={profile.heightCm}
                  unit=" cm"
                  icon={Ruler}
                />
                <DataItem
                  label="Weekly Mileage"
                  value={profile.averageWeeklyKm}
                  icon={Gauge}
                  explanation="Average distance run per week."
                />
                <DataItem
                  label="Avg. Pace (Easy/Long)"
                  value={profile.averagePaceEasyLong}
                  icon={Gauge}
                  explanation="Typical pace for easy or long-distance runs."
                />
                <DataItem
                  label="Running Goal"
                  value={profile.runningGoal}
                  icon={Target}
                  explanation="The runner's primary objective for their training."
                />
                <DataItem
                  label="Primary Terrain"
                  value={profile.terrain}
                  icon={Info}
                  explanation="The most common surface type for runs."
                />
                <DataItem
                  label="Previous Injuries"
                  value={profile.previousInjuries}
                  icon={ShieldAlert}
                  explanation="History of significant running-related injuries."
                />
                {profile.previousInjuriesSeverity && (
                  <DataItem
                    label="Injury Severity"
                    value={profile.previousInjuriesSeverity}
                    icon={ShieldAlert}
                    explanation="Severity of the most recent or significant previous injury."
                  />
                )}
                <DataItem
                  label="Typical Climate"
                  value={profile.climate}
                  icon={Info}
                  explanation="Predominant climate conditions where the runner trains."
                />
                <DataItem
                  label="Avg. Running Cadence"
                  value={profile.averageCadence}
                  unit=" spm"
                  icon={Gauge}
                  explanation="Average steps per minute during typical runs."
                />
              </div>
              <div className="flex justify-end pt-2.5">
                <Button
                  variant="outline"
                  size="sm"
                  asChild
                  className="border-primary/50 text-primary hover:bg-primary/10 hover:text-primary text-xs h-8"
                >
                  <Link href={`/running-profiles/${profile.id}/edit`}>
                    <Edit3 className="mr-1.5 h-3.5 w-3.5" />
                    Edit Information
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
}
