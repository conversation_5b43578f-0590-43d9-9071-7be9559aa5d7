import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Video } from "lucide-react";
import { RunningProfile } from "@prisma/client";

interface VideoAnalysisProps {
  profile: RunningProfile;
}

export function VideoAnalysis({ profile }: VideoAnalysisProps) {
  const renderDetailItem = (
    label: string,
    value: string | number | null | undefined
  ) => {
    if (value === null || value === undefined || value === "") {
      return null;
    }

    return (
      <div className="py-2">
        <div className="flex justify-between">
          <span className="text-sm text-zinc-500 dark:text-zinc-400">
            {label}
          </span>
          <span className="text-sm font-medium text-zinc-900 dark:text-zinc-100">
            {value}
          </span>
        </div>
      </div>
    );
  };

  const renderVideo = (url: string | null, label: string) => {
    if (!url) return null;

    // Check if the URL is valid
    const isValidUrl = url.startsWith("http") || url.startsWith("/");

    return (
      <div className="flex flex-col gap-2 mb-4">
        <h3 className="text-sm font-medium text-zinc-900 dark:text-zinc-100">
          {label}
        </h3>
        <div className="relative w-full aspect-video rounded-md overflow-hidden border border-zinc-200 dark:border-zinc-700">
          {isValidUrl ? (
            <video
              src={url}
              controls
              className="w-full h-full object-cover"
              poster="/images/video-placeholder.jpg"
            />
          ) : (
            <div className="flex items-center justify-center h-full w-full bg-zinc-100 dark:bg-zinc-800">
              <Video className="h-10 w-10 text-zinc-400 dark:text-zinc-600" />
              <p className="ml-2 text-sm text-zinc-500 dark:text-zinc-400">
                Video not available
              </p>
            </div>
          )}
        </div>
      </div>
    );
  };

  const hasVideoData =
    profile.runningVideoPosteriorUrl || profile.runningVideoSagittalUrl;
  const hasAnalysisData =
    profile.pelvicDrop ||
    profile.kneeDrift ||
    profile.footDrift ||
    profile.heelWhip ||
    profile.posteriorStabilityScore ||
    profile.overstride ||
    profile.ankleDorsiflexion ||
    profile.anklePlantarflexion ||
    profile.verticalOscillationVideo ||
    profile.trunkLean ||
    profile.kneeFlexionLoading;

  if (!hasVideoData && !hasAnalysisData) {
    return (
      <Card className="bg-zinc-50 dark:bg-zinc-800/60 border border-zinc-200 dark:border-zinc-700 shadow-sm rounded-lg">
        <CardContent className="py-6">
          <div className="text-center text-zinc-500 dark:text-zinc-400">
            <Video className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>No video analysis data available</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Videos */}
      {hasVideoData && (
        <Card className="bg-zinc-50 dark:bg-zinc-800/60 border border-zinc-200 dark:border-zinc-700 shadow-sm rounded-lg">
          <CardHeader className="pb-2 border-b border-zinc-200 dark:border-zinc-700">
            <CardTitle className="flex items-center gap-2 text-base font-medium text-zinc-900 dark:text-zinc-100">
              <Video className="h-4 w-4 text-zinc-500 dark:text-zinc-400" />
              Running Videos
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-3">
            <div className="space-y-4">
              {renderVideo(profile.runningVideoPosteriorUrl, "Posterior View")}
              {renderVideo(profile.runningVideoSagittalUrl, "Sagittal View")}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Analysis Data */}
      {hasAnalysisData && (
        <Card className="bg-zinc-50 dark:bg-zinc-800/60 border border-zinc-200 dark:border-zinc-700 shadow-sm rounded-lg">
          <CardHeader className="pb-2 border-b border-zinc-200 dark:border-zinc-700">
            <CardTitle className="flex items-center gap-2 text-base font-medium text-zinc-900 dark:text-zinc-100">
              <Video className="h-4 w-4 text-zinc-500 dark:text-zinc-400" />
              Video Analysis
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-3">
            <div className="grid sm:grid-cols-2 gap-x-6 divide-y sm:divide-y-0 divide-zinc-100 dark:divide-zinc-700/50">
              <div className="divide-y divide-zinc-100 dark:divide-zinc-700/50 sm:pr-3 sm:border-r border-zinc-100 dark:border-zinc-700/50">
                {renderDetailItem("Pelvic Drop", profile.pelvicDrop)}
                {renderDetailItem("Knee Drift", profile.kneeDrift)}
                {renderDetailItem("Foot Drift", profile.footDrift)}
                {renderDetailItem("Heel Whip", profile.heelWhip)}
                {renderDetailItem(
                  "Stability Score",
                  profile.posteriorStabilityScore
                )}
                {renderDetailItem("Overstride", profile.overstride)}
              </div>
              <div className="divide-y divide-zinc-100 dark:divide-zinc-700/50 pt-3 sm:pt-0 sm:pl-3">
                {renderDetailItem(
                  "Ankle Dorsiflexion",
                  profile.ankleDorsiflexion
                )}
                {renderDetailItem(
                  "Ankle Plantarflexion",
                  profile.anklePlantarflexion
                )}
                {renderDetailItem(
                  "Vertical Oscillation",
                  profile.verticalOscillationVideo
                    ? `${profile.verticalOscillationVideo} cm`
                    : null
                )}
                {renderDetailItem(
                  "Trunk Lean",
                  profile.trunkLean ? `${profile.trunkLean}°` : null
                )}
                {renderDetailItem(
                  "Knee Flexion Loading",
                  profile.kneeFlexionLoading
                    ? `${profile.kneeFlexionLoading}°`
                    : null
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
