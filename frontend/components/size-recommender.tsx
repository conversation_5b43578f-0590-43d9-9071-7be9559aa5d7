"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { <PERSON>ert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Ruler } from "lucide-react"

interface FootMeasurements {
  length: number
  width: number
  archType: "low" | "medium" | "high"
}

export function SizeRecommender() {
  const [measurements, setMeasurements] = useState<FootMeasurements>({
    length: 0,
    width: 0,
    archType: "medium",
  })
  const [showRecommendation, setShowRecommendation] = useState(false)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setShowRecommendation(true)
  }

  const getRecommendedSize = () => {
    // Simple size calculation based on foot length
    // This is a basic example - in reality, you'd want a more sophisticated algorithm
    const euSize = Math.round((measurements.length + 2) * 1.5)
    const width = measurements.width > 10 ? "wide" : "regular"
    const support = measurements.archType === "high" ? "maximum" : 
                   measurements.archType === "low" ? "minimal" : "balanced"
    
    return {
      euSize,
      width,
      support,
    }
  }

  return (
    <div className="space-y-6">
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid gap-4 sm:grid-cols-2">
          <div>
            <Label htmlFor="length">Foot Length (cm)</Label>
            <Input
              id="length"
              type="number"
              step="0.1"
              min="20"
              max="35"
              value={measurements.length || ""}
              onChange={(e) =>
                setMeasurements({
                  ...measurements,
                  length: parseFloat(e.target.value),
                })
              }
              className="mt-1"
            />
          </div>
          <div>
            <Label htmlFor="width">Foot Width (cm)</Label>
            <Input
              id="width"
              type="number"
              step="0.1"
              min="5"
              max="15"
              value={measurements.width || ""}
              onChange={(e) =>
                setMeasurements({
                  ...measurements,
                  width: parseFloat(e.target.value),
                })
              }
              className="mt-1"
            />
          </div>
        </div>

        <div>
          <Label>Arch Type</Label>
          <RadioGroup
            value={measurements.archType}
            onValueChange={(value: "low" | "medium" | "high") =>
              setMeasurements({ ...measurements, archType: value })
            }
            className="grid grid-cols-3 gap-4 mt-2"
          >
            <div>
              <RadioGroupItem
                value="low"
                id="arch-low"
                className="peer sr-only"
              />
              <Label
                htmlFor="arch-low"
                className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-transparent p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
              >
                <span>Low</span>
              </Label>
            </div>
            <div>
              <RadioGroupItem
                value="medium"
                id="arch-medium"
                className="peer sr-only"
              />
              <Label
                htmlFor="arch-medium"
                className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-transparent p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
              >
                <span>Medium</span>
              </Label>
            </div>
            <div>
              <RadioGroupItem
                value="high"
                id="arch-high"
                className="peer sr-only"
              />
              <Label
                htmlFor="arch-high"
                className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-transparent p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
              >
                <span>High</span>
              </Label>
            </div>
          </RadioGroup>
        </div>

        <Button type="submit" className="w-full">
          Get Size Recommendation
        </Button>
      </form>

      {showRecommendation && measurements.length > 0 && (
        <Alert>
          <Ruler className="h-4 w-4" />
          <AlertTitle>Your Recommended Size</AlertTitle>
          <AlertDescription>
            <div className="mt-2 space-y-2">
              <p>
                <span className="font-medium">EU Size:</span>{" "}
                {getRecommendedSize().euSize}
              </p>
              <p>
                <span className="font-medium">Width:</span>{" "}
                {getRecommendedSize().width}
              </p>
              <p>
                <span className="font-medium">Recommended Support:</span>{" "}
                {getRecommendedSize().support}
              </p>
            </div>
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
} 