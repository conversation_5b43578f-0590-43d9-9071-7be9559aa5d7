"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import {
  LayoutDashboard,
  UserRound,
  Footprints,
  BarChart3,
  Settings,
} from "lucide-react";

interface BottomNavItem {
  title: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  matchExact?: boolean;
}

const bottomNavLinks: BottomNavItem[] = [
  {
    title: "Profiles",
    href: "/running-profiles",
    icon: LayoutDashboard,
    matchExact: true,
  },
  {
    title: "Shoes",
    href: "/shoes",
    icon: Footprints,
  },
  {
    title: "Analytics",
    href: "/analytics",
    icon: BarChart3,
  },
  {
    title: "Settings",
    href: "/settings",
    icon: Settings,
  },
];

export function BottomNavBar() {
  const pathname = usePathname() || "";

  const isActive = (link: BottomNavItem) =>
    link.matchExact ? pathname === link.href : pathname?.startsWith(link.href);

  return (
    <nav className="fixed bottom-0 left-0 right-0 h-16 bg-white dark:bg-sweetspot-black border-t border-zinc-200 dark:border-text-main/15 shadow-[0_-2px_10px_-3px_rgba(0,0,0,0.1)] z-40 sm:hidden">
      <div className="flex justify-around items-center h-full px-2">
        {bottomNavLinks.map((link) => {
          const active = isActive(link);
          return (
            <Link
              key={link.href}
              href={link.href}
              className={cn(
                "flex flex-col items-center justify-center flex-1 h-full text-xs font-medium transition-colors group",
                active
                  ? "text-brand-green dark:text-emerald-300"
                  : "text-zinc-500 dark:text-zinc-400 hover:text-zinc-800 dark:hover:text-zinc-200"
              )}
            >
              <link.icon
                className={cn(
                  "h-5 w-5 mb-0.5 transition-colors",
                  active
                    ? "text-brand-green dark:text-emerald-300"
                    : "text-zinc-400 dark:text-zinc-500 group-hover:text-zinc-600 dark:group-hover:text-zinc-300"
                )}
              />
              <span>{link.title}</span>
            </Link>
          );
        })}
      </div>
    </nav>
  );
}
