"use client"

import { usePathname } from "next/navigation"
import Link from "next/link"
import { cn } from "@/lib/utils"
import { User, Video, Activity, Ruler, Footprints, CreditCard, Check } from "lucide-react"

const steps = [
  {
    title: "Personal Info",
    href: "/onboarding",
    icon: User,
    pattern: "^/onboarding$",
  },
  {
    title: "Foot Scan",
    href: "/onboarding/foot-scan",
    icon: Video,
    optional: true,
    pattern: "^/onboarding/foot-scan",
  },
  {
    title: "Athletic Data",
    href: "/onboarding/athletic-data",
    icon: Activity,
    pattern: "^/onboarding/athletic-data",
  },
  {
    title: "Measurements",
    href: "/onboarding/measurements",
    icon: Ruler,
    pattern: "^/onboarding/measurements",
  },
  {
    title: "Shoe Design",
    href: "/onboarding/shoe-design",
    icon: Footprints,
    pattern: "^/onboarding/shoe-design",
  },
  {
    title: "Checkout",
    href: "/onboarding/checkout",
    icon: CreditCard,
    pattern: "^/onboarding/checkout",
  },
]

export function OnboardingStepper() {
  const pathname = usePathname()

  // Find the current step index by matching the pathname against each step's pattern
  const currentStepIndex = steps.findIndex((step) => 
    pathname && new RegExp(step.pattern).test(pathname)
  )

  return (
    <nav className="grid gap-4 p-4">
      <div className="grid gap-1">
        {steps.map((step, index) => {
          // Determine if the step is completed, current, or upcoming
          const isCompleted = index < currentStepIndex
          const isCurrent = index === currentStepIndex
          const isUpcoming = index > currentStepIndex
          const isClickable = !isUpcoming || index === currentStepIndex + 1

          return (
            <Link
              key={step.href}
              href={isClickable ? step.href : "#"}
              className={cn(
                "flex items-center gap-3 rounded-md px-3 py-2 text-sm transition-colors",
                isCompleted && "text-primary",
                isCurrent && "bg-muted font-medium text-primary",
                !isClickable && "text-muted-foreground pointer-events-none",
                isClickable && !isCurrent && "hover:bg-muted/50",
              )}
            >
              <div
                className={cn(
                  "flex h-7 w-7 items-center justify-center rounded-full border",
                  isCompleted && "bg-primary border-primary text-primary-foreground",
                  isCurrent && "border-primary",
                  !isClickable && "border-muted-foreground",
                )}
              >
                {isCompleted ? <Check className="h-4 w-4" /> : <step.icon className="h-4 w-4" />}
              </div>
              <div className="flex flex-col">
                {step.title}
                {step.optional && <span className="text-xs text-muted-foreground">Optional</span>}
              </div>
            </Link>
          )
        })}
      </div>
    </nav>
  )
}

