"use client"

import { useEffect, useRef } from 'react'
import * as THREE from 'three'
import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
import { GLTFLoader } from 'three/addons/loaders/GLTFLoader.js';

interface ShoeModelProps {
  modelPath: string
  color?: string
  autoRotate?: boolean
  className?: string
}

export function ShoeModel({ 
  modelPath, 
  color = "#2563eb",
  autoRotate = false,
  className = "" 
}: ShoeModelProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const sceneRef = useRef<THREE.Scene | null>(null)
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null)
  const animationFrameRef = useRef<number | null>(null)
  const controlsRef = useRef<OrbitControls | null>(null)
  const currentModelRef = useRef<THREE.Object3D | null>(null)

  useEffect(() => {
    const container = containerRef.current
    if (!container) return

    // Scene setup
    const scene = new THREE.Scene()
    sceneRef.current = scene
    scene.background = null // Transparent background

    // Camera setup
    const camera = new THREE.PerspectiveCamera(
      45,
      container.clientWidth / container.clientHeight,
      0.1,
      1000
    )
    camera.position.set(1.5, 1, 1.5) // Closer camera position
    camera.lookAt(0, 0, 0)

    // Renderer setup
    const renderer = new THREE.WebGLRenderer({ 
      antialias: true,
      alpha: true 
    })
    rendererRef.current = renderer
    renderer.setSize(container.clientWidth, container.clientHeight)
    renderer.setPixelRatio(window.devicePixelRatio)
    renderer.outputColorSpace = THREE.SRGBColorSpace
    renderer.toneMapping = THREE.ACESFilmicToneMapping
    renderer.toneMappingExposure = 1
    container.appendChild(renderer.domElement)

    // Lighting setup
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.5)
    scene.add(ambientLight)

    const keyLight = new THREE.DirectionalLight(0xffffff, 1)
    keyLight.position.set(2, 2, 2)
    scene.add(keyLight)

    const fillLight = new THREE.DirectionalLight(0xffffff, 0.5)
    fillLight.position.set(-2, -2, -2)
    scene.add(fillLight)

    // Controls setup
    const controls = new OrbitControls(camera, renderer.domElement)
    controlsRef.current = controls
    controls.enableDamping = true
    controls.dampingFactor = 0.05
    controls.minDistance = 1.5
    controls.maxDistance = 4
    controls.autoRotate = autoRotate
    controls.autoRotateSpeed = 2
    controls.enablePan = false
    controls.target.set(0, 0, 0)

    // Model loading
    const loader = new GLTFLoader()

    loader.load(
      modelPath,
      (gltf) => {
        // Remove previous model if it exists
        if (currentModelRef.current && sceneRef.current) {
          sceneRef.current.remove(currentModelRef.current)
          currentModelRef.current.traverse((child) => {
            if (child instanceof THREE.Mesh) {
              child.geometry.dispose()
              if (Array.isArray(child.material)) {
                child.material.forEach(material => material.dispose())
              } else {
                child.material.dispose()
              }
            }
          })
        }

        const model = gltf.scene
        currentModelRef.current = model
        
        // Center and scale the model
        const box = new THREE.Box3().setFromObject(model)
        const center = box.getCenter(new THREE.Vector3())
        const size = box.getSize(new THREE.Vector3())
        const maxDim = Math.max(size.x, size.y, size.z)
        const scale = 1 / maxDim
        
        model.position.sub(center.multiplyScalar(scale))
        model.scale.multiplyScalar(scale)

        // Apply color if provided
        if (color) {
          model.traverse((child) => {
            if (child instanceof THREE.Mesh) {
              if (child.material) {
                if (Array.isArray(child.material)) {
                  child.material.forEach(material => material.dispose())
                } else {
                  child.material.dispose()
                }
              }
              child.material = new THREE.MeshStandardMaterial({
                color: new THREE.Color(color),
                metalness: 0.2,
                roughness: 0.3,
              })
            }
          })
        }

        scene.add(model)
      },
      undefined,
      (error) => {
        console.error("Error loading model:", error)
      }
    )

    // Animation loop
    function animate() {
      animationFrameRef.current = requestAnimationFrame(animate)
      controls.update()
      renderer.render(scene, camera)
    }
    animate()

    // Handle window resize
    function handleResize() {
      if (!container) return
      
      const width = container.clientWidth
      const height = container.clientHeight
      
      camera.aspect = width / height
      camera.updateProjectionMatrix()
      renderer.setSize(width, height)
    }
    window.addEventListener("resize", handleResize)

    // Cleanup
    return () => {
      // Cancel animation frame
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
        animationFrameRef.current = null
      }

      // Remove event listeners
      window.removeEventListener("resize", handleResize)

      // Dispose of controls
      if (controlsRef.current) {
        controlsRef.current.dispose()
        controlsRef.current = null
      }

      // Clean up the scene
      if (sceneRef.current && currentModelRef.current) {
        // First remove and dispose of the current model
        sceneRef.current.remove(currentModelRef.current)
        currentModelRef.current.traverse((child) => {
          if (child instanceof THREE.Mesh) {
            child.geometry.dispose()
            if (Array.isArray(child.material)) {
              child.material.forEach(material => material.dispose())
            } else {
              child.material.dispose()
            }
          }
        })
        currentModelRef.current = null

        // Then remove all remaining objects (like lights)
        const objects = [...sceneRef.current.children]
        objects.forEach(object => {
          sceneRef.current?.remove(object)
          if (object instanceof THREE.Light) {
            object.dispose()
          }
        })
      }

      // Remove renderer from DOM and dispose
      if (container && rendererRef.current?.domElement) {
        container.removeChild(rendererRef.current.domElement)
      }
      if (rendererRef.current) {
        rendererRef.current.dispose()
        rendererRef.current.forceContextLoss()
        rendererRef.current = null
      }

      // Clear scene reference
      sceneRef.current = null
    }
  }, [modelPath, color, autoRotate])

  return <div ref={containerRef} className={className} />
}

