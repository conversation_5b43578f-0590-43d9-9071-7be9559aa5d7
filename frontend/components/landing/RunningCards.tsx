"use client";

export default function RunningCards() {
  return (
    <div className="w-full max-w-xl sm:max-w-2xl md:max-w-4xl mx-auto px-0 py-2 pb-0">
      <div className="text-left mb-10">
        <h1 className="text-4xl font-sans font-bold mb-2">
          Your stride. Your shoe.
        </h1>
        <p className="text-lg font-sans">
          AI-matched running shoes, powered by your data. Free to use.
        </p>
      </div>

      <div className="relative">
        {/* Two column layout with staggered cards */}
        <div className="flex gap-2 sm:gap-3 md:gap-4">
          {/* Left column */}
          <div className="w-[49%] flex flex-col gap-3 sm:gap-4 md:gap-5">
            {/* Stride Analysis Card */}
            <div
              className="aspect-[4/5] sm:aspect-square rounded-lg overflow-hidden relative shadow-lg"
              style={{
                backgroundImage: "url('/images/runner_card.png')",
                backgroundSize: "cover",
                backgroundPosition: "center",
              }}
            >
              <div className="absolute inset-0">
                <div className="p-3 sm:p-4 text-white h-full flex flex-col justify-between">
                  <h3 className="font-mono text-sm sm:text-base md:text-lg font-bold text-left">
                    Stride Analysis
                  </h3>
                  <p className="font-mono text-xs sm:text-sm leading-tight text-left mt-auto">
                    Computer vision breaks down your stride—frame by frame,
                    joint by joint.
                  </p>
                </div>
              </div>
            </div>

            {/* Wearable Data Card */}
            <div
              className="aspect-[4/5] sm:aspect-square rounded-lg overflow-hidden relative shadow-lg"
              style={{
                backgroundImage: "url('/images/wearable_card.png')",
                backgroundSize: "cover",
                backgroundPosition: "center",
              }}
            >
              <div className="absolute inset-0">
                <div className="p-3 sm:p-4 text-white h-full flex flex-col justify-between">
                  <h3 className="font-mono text-sm sm:text-base md:text-lg font-bold text-left">
                    Wearable Data
                  </h3>
                  <p className="font-mono text-xs sm:text-sm leading-tight text-left mt-auto">
                    Your watch knows how and when your form fades. Your shoes
                    should too.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Right column - staggered down */}
          <div className="w-[49%] flex flex-col gap-3 sm:gap-4 md:gap-5 mt-8">
            {/* Foot Structure Card */}
            <div
              className="aspect-[4/5] sm:aspect-square rounded-lg overflow-hidden relative shadow-lg"
              style={{
                backgroundImage: "url('/images/topology_card.png')",
                backgroundSize: "cover",
                backgroundPosition: "center",
              }}
            >
              <div className="absolute inset-0">
                <div className="p-3 sm:p-4 text-white h-full flex flex-col justify-between">
                  <h3 className="font-mono text-sm sm:text-base md:text-lg font-bold text-left">
                    Foot Structure
                  </h3>
                  <p className="font-mono text-xs sm:text-sm leading-tight text-left mt-auto">
                    Foot geometry and structure – used to assess fit, arch
                    support needs, and instep height for better comfort and
                    stability.
                  </p>
                </div>
              </div>
            </div>

            {/* Injury & Conditions Card */}
            <div
              className="aspect-[4/5] sm:aspect-square rounded-lg overflow-hidden relative shadow-lg"
              style={{
                backgroundImage: "url('/images/knee_card.png')",
                backgroundSize: "cover",
                backgroundPosition: "center",
              }}
            >
              <div className="absolute inset-0">
                <div className="p-3 sm:p-4 text-white h-full flex flex-col justify-between">
                  <h3 className="font-mono text-sm sm:text-base md:text-lg font-bold text-left">
                    Injury & Conditions
                  </h3>
                  <p className="font-mono text-xs sm:text-sm leading-tight text-left mt-auto">
                    Past pains, cold climates, rough terrain - your needs are
                    individual. This isn't guesswork.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
