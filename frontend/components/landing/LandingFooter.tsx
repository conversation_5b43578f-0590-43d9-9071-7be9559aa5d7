"use client";

import Link from "next/link";

export function LandingFooter() {
  return (
    <footer className="relative p-5 sm:p-6 bg-white border-t border-border/30 z-10">
      <div className="max-w-7xl mx-auto text-center font-light text-xs sm:text-sm text-muted-foreground space-y-2 px-4 sm:px-6">
        <p className="font-mono">
          <span className="text-primary/80 mr-1">//</span> &copy;{" "}
          {new Date().getFullYear()} eigen{" "}
          <span className="text-primary/80">by</span> CrossPath Labs
        </p>

        <p className="max-w-xl mx-auto font-jetbrains">
          Transparency: Equal, standardized affiliate payment
          post-recommendation. Your data drives results, not brand deals.
        </p>

        <div className="pt-3 space-x-4 flex justify-center items-center">
          <Link
            href="/privacy"
            className="relative hover:text-primary font-input"
          >
            Privacy Policy
          </Link>
          <span className="text-primary/30">|</span>
          <Link
            href="/terms"
            className="relative hover:text-primary font-input"
          >
            Terms of Service
          </Link>
          <span className="text-primary/30">|</span>
          <Link href="#" className="relative hover:text-primary font-input">
            Contact
          </Link>
        </div>

        <div className="pt-2 text-[10px] text-muted-foreground/60 font-input">
          <span className="text-primary/50 mr-1">//</span> Built with precision
          for runners, by runners
        </div>
      </div>
    </footer>
  );
}
