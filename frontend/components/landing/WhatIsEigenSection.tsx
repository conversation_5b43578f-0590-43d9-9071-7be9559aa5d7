"use client";

import { motion } from "framer-motion";

const fadeInVariant = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.6, ease: "easeOut" },
  },
};

const staggerVariant = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1,
    },
  },
};

export function WhatIsEigenSection() {
  return (
    <motion.section
      className="relative py-10 sm:py-12 bg-white border-t border-b border-border/20"
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.1 }}
      variants={staggerVariant}
    >
      <div className="max-w-3xl mx-auto px-4 sm:px-6 z-10 relative">
        <motion.div
          variants={fadeInVariant}
          className="p-5 bg-white border border-border/30 rounded-lg shadow-md relative z-10 overflow-hidden"
        >
          <div className="relative">
            <h2 className="font-sans text-3xl sm:text-4xl font-medium text-foreground mb-4 flex items-center">
              <span className="text-primary/80 mr-2">//</span> What is{" "}
              <span className="text-primary ml-2 relative">
                eigen
                <motion.span
                  className="absolute -bottom-1 left-0 w-full h-0.5 bg-primary/50"
                  initial={{ width: 0 }}
                  whileInView={{ width: "100%" }}
                  viewport={{ once: true }}
                  transition={{ duration: 1, ease: "easeOut" }}
                ></motion.span>
              </span>
              ?
            </h2>
            <div className="space-y-3 font-jetbrains text-sm sm:text-base text-muted-foreground p-2">
              <p>
                &gt; A biomechanical analysis platform that matches your
                unique running characteristics to the perfect shoes.
              </p>
              <p>
                &gt; We analyze foot structure, gait patterns, and running
                preferences to create your personalized profile.
              </p>
              <p>
                &gt; Our algorithms identify the precise shoe
                characteristics that will optimize your performance and
                reduce injury risk.
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    </motion.section>
  );
}
