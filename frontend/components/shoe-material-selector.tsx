"use client"

import { useState } from "react"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"

const materialOptions = [
  {
    id: "engineered-mesh",
    name: "Engineered Mesh",
    description: "Lightweight and breathable",
  },
  {
    id: "knit",
    name: "Performance Knit",
    description: "Sock-like fit and comfort",
  },
  {
    id: "recycled",
    name: "Recycled Materials",
    description: "Eco-friendly and sustainable",
  },
  {
    id: "waterproof",
    name: "Waterproof Membrane",
    description: "Protection from wet conditions",
  },
]

export function ShoeMaterialSelector() {
  const [material, setMaterial] = useState("engineered-mesh")

  return (
    <div className="space-y-4">
      <h3 className="text-sm font-medium">Upper Material</h3>
      <RadioGroup value={material} onValueChange={setMaterial} className="grid gap-3">
        {materialOptions.map((option) => (
          <div key={option.id} className="flex items-center space-x-2">
            <RadioGroupItem value={option.id} id={option.id} className="peer sr-only" />
            <Label
              htmlFor={option.id}
              className="flex flex-1 items-center justify-between rounded-md border-2 border-muted bg-popover p-3 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary"
            >
              <div>
                <div className="font-medium">{option.name}</div>
                <div className="text-sm text-muted-foreground">{option.description}</div>
              </div>
              <div className="h-4 w-4 rounded-full border border-primary"></div>
            </Label>
          </div>
        ))}
      </RadioGroup>
    </div>
  )
}

