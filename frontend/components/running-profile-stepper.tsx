"use client";

import { cn } from "@/lib/utils";
import Link from "next/link";
import { useRunningProfile } from "@/app/contexts/RunningProfileContext";
import { usePathname } from "next/navigation";

export function RunningProfileStepper() {
  const { currentStep, setCurrentStep, steps } = useRunningProfile();
  const pathname = usePathname() || "";
  
  // Don't show this component on the running-profiles (plural) page
  // Only show on the running-profile (singular) page or its subpages
  if (pathname.startsWith("/running-profiles")) {
    return null;
  }
  
  // Check if we're in mobile view (being rendered in the sheet)
  // const isMobileView = pathname === pathname && document?.querySelector('[data-state="open"]')?.contains(document?.activeElement);

  // Determine which steps are completed based on the context state
  const getStepStatus = (stepHref: string) => {
    const stepIndex = steps.findIndex(step => step.href === stepHref);
    
    if (stepIndex < currentStep) {
      return "completed";
    } else if (stepIndex === currentStep) {
      return "active";
    } else {
      return "pending";
    }
  };

  // Update current step when navigating
  const handleStepClick = (index: number) => {
    // Only allow going back or to the next step
    if (index <= currentStep + 1) {
      setCurrentStep(index);
    }
  };

  return (
    <nav className="grid gap-2 md:gap-4 text-sm">
      <h2 className="text-base md:text-lg font-semibold">Create Running Profile</h2>
      <div className="grid gap-1 md:gap-2">
        {steps.map((step, index) => {
          const status = getStepStatus(step.href);
          const isActive = status === "active";
          const isCompleted = status === "completed";
          const isClickable = index <= currentStep + 1;

          return (
            <Link
              key={step.href}
              href={isClickable ? step.href : "#"}
              onClick={(e) => {
                if (!isClickable) {
                  e.preventDefault();
                  return;
                }
                handleStepClick(index);
              }}
              className={cn(
                "flex flex-col gap-1 rounded-md border p-2 md:p-3 transition-colors",
                isClickable ? "hover:bg-muted active:bg-muted" : "opacity-60 cursor-not-allowed",
                isActive && "bg-muted border-primary"
              )}
            >
              <div className="flex items-center gap-2">
                <div
                  className={cn(
                    "flex h-5 w-5 md:h-6 md:w-6 items-center justify-center rounded-full border text-xs",
                    isActive && "border-primary bg-primary text-primary-foreground",
                    isCompleted && "border-primary bg-primary text-primary-foreground"
                  )}
                >
                  {isCompleted ? "✓" : index + 1}
                </div>
                <span className="font-medium text-xs md:text-sm">{step.title}</span>
              </div>
              <p className="text-muted-foreground text-xs pl-7 md:pl-8 line-clamp-2">
                {step.description}
              </p>
            </Link>
          );
        })}
      </div>
    </nav>
  );
} 