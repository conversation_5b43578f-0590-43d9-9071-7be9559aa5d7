"use client";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { LogOut, Settings, LayoutDashboard } from "lucide-react";
import Link from "next/link";
import { useAuth } from "@/contexts/AuthContext";

// Updated UserNav component
export default function UserNav() {
  const { user, signOut } = useAuth();

  if (!user) {
    return null;
  }

  const userInitial = user.user_metadata?.name
    ? user.user_metadata.name.charAt(0).toUpperCase()
    : user.email?.charAt(0).toUpperCase() || "?";

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="relative h-8 w-8 rounded-full focus-visible:ring-0 focus-visible:ring-offset-0 border border-zinc-300 hover:border-zinc-400 transition-colors p-0"
        >
          <Avatar className="h-8 w-8">
            <AvatarImage
              src={user.user_metadata?.image ?? undefined}
              alt={user.user_metadata?.name ?? "User"}
            />
            <AvatarFallback className="bg-zinc-100 text-zinc-700 text-xs font-mono">
              {userInitial}
            </AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        className="w-56 bg-white border-zinc-200 text-zinc-800 font-input rounded-md shadow-xl"
        align="end"
        forceMount
      >
        <DropdownMenuLabel className="font-normal pt-2 pb-1 px-2">
          <div className="flex flex-col space-y-0.5">
            <p className="text-sm font-medium leading-none text-zinc-800 truncate">
              {user.user_metadata?.name ?? "User"}
            </p>
            <p className="text-xs leading-none text-zinc-500 truncate">
              {user.email}
            </p>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator className="bg-zinc-200 mx-1" />
        <DropdownMenuItem
          asChild
          className="cursor-pointer hover:bg-zinc-100 focus:bg-zinc-100 focus:text-zinc-800 m-1 rounded-sm h-8"
        >
          <Link href="/running-profiles">
            <LayoutDashboard className="mr-2 h-4 w-4 text-zinc-500" />
            <span>Running Profiles</span>
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem
          asChild
          className="cursor-pointer hover:bg-zinc-100 focus:bg-zinc-100 focus:text-zinc-800 m-1 rounded-sm h-8"
        >
          <Link href="/settings">
            <Settings className="mr-2 h-4 w-4 text-zinc-500" />
            <span>Account Settings</span>
          </Link>
        </DropdownMenuItem>
        <DropdownMenuSeparator className="bg-zinc-200 mx-1" />
        <DropdownMenuItem
          onClick={() => signOut()}
          className="cursor-pointer hover:bg-zinc-100 focus:bg-zinc-100 text-red-600 focus:text-red-700 m-1 rounded-sm h-8"
        >
          <LogOut className="mr-2 h-4 w-4" />
          <span>Log out</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
