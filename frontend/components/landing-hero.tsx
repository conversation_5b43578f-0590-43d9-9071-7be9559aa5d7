"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowRight, Play } from "lucide-react"
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog"

export function LandingHero() {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  return (
    <section className="w-full py-12 md:py-24 lg:py-32 xl:py-48 bg-background">
      <div className="container px-4 md:px-6">
        <div className="grid gap-6 lg:grid-cols-[1fr_400px] lg:gap-12 xl:grid-cols-[1fr_600px]">
          <div className="flex flex-col justify-center space-y-4">
            <div className="space-y-2">
              <h1 className="text-3xl font-bold tracking-tighter sm:text-5xl xl:text-6xl/none text-primary">
                Your Perfect Shoe, Engineered for You.
              </h1>
              <p className="max-w-[600px] text-muted-foreground md:text-xl">
                Personalized athletic footwear designed using your unique biomechanics, running data, and preferences
                for optimal performance and comfort.
              </p>
            </div>
            <div className="flex flex-col gap-2 min-[400px]:flex-row">
              <Link href="/signup">
                <Button size="lg" className="bg-primary hover:bg-primary/90">
                  Get Started <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </Link>
              {isClient && (
                <Dialog>
                  <DialogTrigger asChild>
                    <Button size="lg" variant="outline" className="gap-2">
                      <Play className="h-4 w-4" />
                      Watch Demo
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-[800px] p-0 bg-black">
                    <video
                      className="w-full aspect-video"
                      controls
                      autoPlay
                      src="/placeholder.svg?height=450&width=800"
                    >
                      Your browser does not support the video tag.
                    </video>
                  </DialogContent>
                </Dialog>
              )}
            </div>
          </div>
          <div className="hidden lg:block relative">
            <div className="absolute inset-0 bg-gradient-to-r from-background to-transparent z-10" />
            <div className="w-full h-full rounded-xl overflow-hidden">
              <div className="w-full h-full bg-muted rounded-xl flex items-center justify-center">
                <div className="relative w-[300px] h-[300px]">
                  {/* This will be replaced with the 3D shoe model */}
                  <div className="absolute inset-0 flex items-center justify-center">
                    <span className="text-muted-foreground">3D Shoe Preview</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

