import { useState } from "react";
import { createClient } from "@/utils/supabase/client";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useRouter } from "next/navigation";
import { z } from "zod";
import { Eye, EyeOff, Mail, Loader2 } from "lucide-react";
import { Separator } from "@/components/ui/separator";

const passwordSigninSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
  password: z.string().min(1, "Password is required"),
});

const magicLinkSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
});

type PasswordSigninForm = z.infer<typeof passwordSigninSchema>;

export default function SignInForm() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [loadingMagicLink, setLoadingMagicLink] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [formData, setFormData] = useState<PasswordSigninForm>({
    email: "",
    password: "",
  });
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Clear specific field error when typing
    if (errors[name]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[name];
        // Keep form error if present
        if (
          errors.form &&
          !Object.keys(newErrors).filter((k) => k !== "form").length
        ) {
          // If only form error remains, don't clear it yet
        } else if (errors.form) {
          newErrors.form = errors.form; // Preserve form error if other fields are being corrected
        } else {
          delete newErrors.form; // Clear form error if no other errors exist
        }
        return newErrors;
      });
    }
    // Clear form error when typing in any field if it exists
    else if (errors.form) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors.form;
        return newErrors;
      });
    }
  };

  const handlePasswordSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setErrors({}); // Clear previous errors
    setSuccess(null); // Clear success message

    try {
      // Validate form data
      const validatedData = passwordSigninSchema.parse(formData);

      console.log("Attempting to sign in with:", validatedData.email);

      // Call Supabase Auth signIn method
      const supabase = createClient();
      const { data, error } = await supabase.auth.signInWithPassword({
        email: validatedData.email,
        password: validatedData.password,
      });

      console.log("Sign in response:", { data, error });

      if (error) {
        // Check if this is an email verification error
        if (error.message.includes("Email not confirmed")) {
          console.error("Email not confirmed error:", error);
          throw new Error(
            "Please verify your email before signing in. Check your inbox for a verification link."
          );
        } else {
          console.error("Authentication error:", error);
          throw new Error("Invalid email or password. Please try again.");
        }
      }

      // Check if we have a session and user
      if (!data.session || !data.user) {
        console.error("No session or user after successful sign in");
        throw new Error(
          "Sign in succeeded but no session was created. Please try again."
        );
      }

      console.log("Successfully signed in user:", data.user.id);

      // Check if user exists in User table
      const { data: userData, error: userError } = await supabase
        .from("User")
        .select("*")
        .eq("id", data.user.id)
        .single();

      console.log("User check result:", { userData, userError });

      // If user doesn't exist, create it
      if (userError && userError.code === "PGRST116") {
        // PGRST116 is "no rows returned"
        console.log("No user found, creating one...");
        const { data: newUser, error: createError } = await supabase
          .from("User")
          .insert([
            {
              id: data.user.id,
              name:
                data.user.user_metadata.name ||
                data.user.email?.split("@")[0] ||
                "User",
              email: data.user.email,
            },
          ])
          .select()
          .single();

        console.log("User creation result:", { newUser, createError });

        if (createError) {
          console.error("Error creating user:", createError);
          // Continue anyway, we'll try to redirect
        }
      }

      // Redirect to running profiles after successful login
      console.log("Redirecting to running profiles...");
      router.push("/running-profiles");
    } catch (error) {
      if (error instanceof z.ZodError) {
        // Handle validation errors
        const newErrors: { [key: string]: string } = {};
        error.errors.forEach((err) => {
          if (err.path[0]) {
            newErrors[err.path[0] as string] = err.message;
          }
        });
        setErrors(newErrors);
      } else if (error instanceof Error) {
        // Handle authentication or other errors
        setErrors({ form: error.message });
      } else {
        // Fallback error message
        setErrors({ form: "An unexpected error occurred during sign in." });
      }
    } finally {
      setLoading(false);
    }
  };

  const handleMagicLinkSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoadingMagicLink(true);
    setErrors({}); // Clear previous errors
    setSuccess(null); // Clear success message

    try {
      // Validate email
      const validatedData = magicLinkSchema.parse({ email: formData.email });

      console.log("Attempting to send magic link to:", validatedData.email);

      // Call Supabase Auth signInWithOtp method
      const supabase = createClient();
      const { error } = await supabase.auth.signInWithOtp({
        email: validatedData.email,
        options: {
          shouldCreateUser: true,
          emailRedirectTo: `${window.location.origin}/auth/callback`,
        },
      });

      if (error) {
        console.error("Magic link error:", error);
        throw new Error(
          error.message || "Failed to send magic link. Please try again."
        );
      }

      // Show success message
      setSuccess("Magic link sent! Check your email inbox for a sign-in link.");
    } catch (error) {
      if (error instanceof z.ZodError) {
        // Handle validation errors
        const newErrors: { [key: string]: string } = {};
        error.errors.forEach((err) => {
          if (err.path[0]) {
            newErrors[err.path[0] as string] = err.message;
          }
        });
        setErrors(newErrors);
      } else if (error instanceof Error) {
        // Handle authentication or other errors
        setErrors({ form: error.message });
      } else {
        // Fallback error message
        setErrors({ form: "An unexpected error occurred. Please try again." });
      }
    } finally {
      setLoadingMagicLink(false);
    }
  };

  return (
    <div className="space-y-6 w-full">
      {/* Error message */}
      {errors.form && (
        <div className="bg-red-500/10 dark:bg-syntax-error/10 border border-red-500/30 dark:border-syntax-error/30 text-red-700 dark:text-syntax-error text-xs p-3 rounded-sm font-mono">
          {errors.form}
        </div>
      )}

      {/* Success message */}
      {success && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 text-green-800 dark:text-green-300 text-xs p-3 rounded-sm font-mono">
          {success}
        </div>
      )}

      {/* Email Field (Shared) */}
      <div className="space-y-1.5">
        <Label
          htmlFor="email"
          className="text-xs font-medium text-brand-black/70 dark:text-text-main/70"
        >
          Email
        </Label>
        <Input
          id="email"
          name="email"
          placeholder="<EMAIL>"
          type="email"
          autoCapitalize="none"
          autoComplete="email"
          autoCorrect="off"
          value={formData.email}
          onChange={handleChange}
          disabled={loading || loadingMagicLink}
          className="themed-input"
        />
        {errors.email && !errors.form && (
          <p className="text-red-700 dark:text-syntax-error text-xs mt-1 font-mono">
            {errors.email}
          </p>
        )}
      </div>

      {/* Password Sign In Form */}
      <form onSubmit={handlePasswordSignIn} className="space-y-4">
        <div className="space-y-1.5">
          <div className="flex items-center justify-between">
            <Label
              htmlFor="password"
              className="text-xs font-medium text-brand-black/70 dark:text-text-main/70"
            >
              Password
            </Label>
          </div>
          <div className="relative">
            <Input
              id="password"
              name="password"
              placeholder=">********"
              type={showPassword ? "text" : "password"}
              autoCapitalize="none"
              autoComplete="current-password"
              value={formData.password}
              onChange={handleChange}
              disabled={loading}
              className="themed-input pr-10"
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-1.5 top-1/2 -translate-y-1/2 h-7 w-7 p-0 text-brand-black/50 dark:text-text-main/50 hover:text-brand-black dark:hover:text-text-main hover:bg-zinc-100/50 dark:hover:bg-sweetspot-black/50"
              onClick={() => setShowPassword(!showPassword)}
              disabled={loading}
            >
              {showPassword ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
              <span className="sr-only">
                {showPassword ? "Hide password" : "Show password"}
              </span>
            </Button>
          </div>
          {errors.password && !errors.form && (
            <p className="text-red-700 dark:text-syntax-error text-xs mt-1 font-mono">
              {errors.password}
            </p>
          )}
        </div>

        <Button
          type="submit"
          className="w-full bg-brand-black text-off-white dark:bg-off-white dark:text-text-inverted hover:bg-brand-black/90 dark:hover:bg-off-white/90 font-sans text-sm h-9 group relative overflow-hidden transition-colors duration-300 disabled:opacity-60"
          disabled={loading || !formData.email}
        >
          {loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Authenticating...
            </>
          ) : (
            <span className="relative z-10">
              Sign in with Password &gt;
            </span>
          )}
        </Button>
      </form>

      {/* Divider */}
      <div className="relative my-6">
        <Separator className="my-4" />
        <div className="absolute inset-0 flex items-center justify-center">
          <span className="bg-white dark:bg-sweetspot-black px-2 text-xs text-brand-black/50 dark:text-text-main/50">
            or
          </span>
        </div>
      </div>

      {/* Magic Link Sign In */}
      <div className="text-center mb-4">
        <div className="flex items-center justify-center gap-2">
          <Mail className="h-4 w-4 text-brand-black/70 dark:text-text-main/70" />
          <span className="text-sm font-medium text-brand-black/90 dark:text-text-main/90">
            Sign in with Magic Link
          </span>
        </div>
        <p className="text-xs text-brand-black/60 dark:text-text-main/60 mt-2 max-w-xs mx-auto">
          We&apos;ll send you a magic link to your email that will sign
          you in instantly.
        </p>
      </div>

      <form onSubmit={handleMagicLinkSignIn} className="space-y-4">
        <Button
          type="submit"
          variant="outline"
          className="w-full border-brand-black/20 dark:border-text-main/20 text-brand-black dark:text-text-main hover:bg-brand-black/5 dark:hover:bg-text-main/5 font-sans text-sm h-9"
          disabled={loadingMagicLink || !formData.email}
        >
          {loadingMagicLink ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Sending magic link...
            </>
          ) : (
            <span>Sign in with Magic Link &gt;</span>
          )}
        </Button>
      </form>
    </div>
  );
}
