"use client"

import { useState } from "react"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"
import { Switch } from "@/components/ui/switch"

export function ShoeFeatureSelector() {
  const [cushioning, setCushioning] = useState([50])
  const [dropHeight, setDropHeight] = useState([8])
  const [stability, setStability] = useState([30])
  const [extraFeatures, setExtraFeatures] = useState({
    reflective: false,
    wideToe: true,
    extraTraction: false,
  })

  const handleFeatureChange = (feature: string) => {
    setExtraFeatures({
      ...extraFeatures,
      [feature]: !extraFeatures[feature as keyof typeof extraFeatures],
    })
  }

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <h3 className="text-sm font-medium">Performance Features</h3>

        <div className="space-y-2">
          <div className="flex justify-between">
            <Label htmlFor="cushioning">Cushioning</Label>
            <span className="text-sm text-muted-foreground">
              {cushioning[0] < 30 ? "Minimal" : cushioning[0] < 70 ? "Balanced" : "Maximum"}
            </span>
          </div>
          <Slider id="cushioning" min={0} max={100} step={1} value={cushioning} onValueChange={setCushioning} />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>Responsive</span>
            <span>Plush</span>
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex justify-between">
            <Label htmlFor="drop">Heel-to-Toe Drop</Label>
            <span className="text-sm text-muted-foreground">{dropHeight[0]}mm</span>
          </div>
          <Slider id="drop" min={0} max={12} step={1} value={dropHeight} onValueChange={setDropHeight} />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>0mm (Zero Drop)</span>
            <span>12mm</span>
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex justify-between">
            <Label htmlFor="stability">Stability Control</Label>
            <span className="text-sm text-muted-foreground">
              {stability[0] < 30 ? "Neutral" : stability[0] < 70 ? "Moderate" : "Maximum"}
            </span>
          </div>
          <Slider id="stability" min={0} max={100} step={1} value={stability} onValueChange={setStability} />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>Flexible</span>
            <span>Structured</span>
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-sm font-medium">Additional Features</h3>

        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label htmlFor="reflective" className="cursor-pointer">
              Reflective Elements
            </Label>
            <Switch
              id="reflective"
              checked={extraFeatures.reflective}
              onCheckedChange={() => handleFeatureChange("reflective")}
            />
          </div>

          <div className="flex items-center justify-between">
            <Label htmlFor="wideToe" className="cursor-pointer">
              Wide Toe Box
            </Label>
            <Switch
              id="wideToe"
              checked={extraFeatures.wideToe}
              onCheckedChange={() => handleFeatureChange("wideToe")}
            />
          </div>

          <div className="flex items-center justify-between">
            <Label htmlFor="extraTraction" className="cursor-pointer">
              Extra Traction Outsole
            </Label>
            <Switch
              id="extraTraction"
              checked={extraFeatures.extraTraction}
              onCheckedChange={() => handleFeatureChange("extraTraction")}
            />
          </div>
        </div>
      </div>
    </div>
  )
}

