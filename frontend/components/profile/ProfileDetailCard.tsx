import { ReactNode } from "react";
import { Card } from "@/components/ui/card";
import { ChevronDown, ChevronUp } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";

interface ProfileDetailCardProps {
  title: string;
  icon: ReactNode;
  isExpanded: boolean;
  onToggleExpand?: () => void;
  showToggle?: boolean;
  children: ReactNode;
}

export function ProfileDetailCard({
  title,
  icon,
  isExpanded,
  onToggleExpand,
  showToggle = false,
  children,
}: ProfileDetailCardProps) {
  return (
    <Card className="border-border/50 overflow-hidden">
      <div
        className={cn(
          "transition-all duration-300",
          isExpanded ? "h-[400px] overflow-y-auto" : "overflow-visible",
          (title || (icon && icon !== <></>) || showToggle) ? "p-6" : "px-6 pb-6 pt-2"
        )}
      >
        {(title || (icon && icon !== <></>) || showToggle) && (
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-base font-medium flex items-center">
              {icon}
              {title}
            </h3>

            {showToggle && onToggleExpand && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onToggleExpand}
                className="text-xs"
              >
                {isExpanded ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
            )}
          </div>
        )}

        <div>{children}</div>
      </div>
    </Card>
  );
}
