"use client";

import Link from "next/link";
import Image from "next/image";

import { ChevronRight } from "lucide-react";
import UserNav from "@/components/user-nav";
import { Sheet, SheetTrigger } from "@/components/ui/sheet";
import { useState } from "react";

import { usePathname } from "next/navigation";

// Helper to generate title from pathname
const getPageTitle = (pathname: string): string => {
  if (!pathname) return "Dashboard";
  const pathSegments = pathname.split("/").filter(Boolean);
  if (pathSegments.length === 0) return "Dashboard";

  const lastSegment = pathSegments[pathSegments.length - 1];

  // Handle specific routes or dynamic segments if needed
  if (lastSegment === "running-profiles") return "Running Profiles";
  if (lastSegment === "shoes") return "My Shoes";
  if (lastSegment === "analytics") return "Analytics";
  if (lastSegment === "settings") return "Settings";
  if (pathSegments[0] === "running-profiles" && pathSegments.length > 1)
    return "Profile Details"; // Example for sub-page
  // Add more cases as needed

  // Default: Capitalize and replace dashes
  return lastSegment
    .split("-")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
};

export function DashboardHeader() {
  const [isOpen, setIsOpen] = useState(false);
  const pathname = usePathname();
  const pageTitle = getPageTitle(pathname);

  return (
    <header className="sticky top-0 z-50 w-full border-b border-brand-black/15 bg-white/80 backdrop-blur-md shadow-sm">
      <div className="flex h-14 items-center justify-between px-4 sm:px-6">
        <div className="flex items-center gap-2">
          <Sheet open={isOpen} onOpenChange={setIsOpen}>
            <SheetTrigger asChild>
              {/* Hamburger button removed - Mobile nav handled by BottomNavBar */}
            </SheetTrigger>
          </Sheet>

          <div className="flex items-center gap-1.5 sm:gap-2">
            <Link href="/" className="flex items-center gap-2 sm:gap-3">
              <div className="bg-off-white p-1 rounded-sm flex items-center justify-center">
                <Image
                  src="/logo.png"
                  alt="Eigen Logo"
                  width={80}
                  height={80}
                  className="h-5 w-auto"
                />
              </div>
              <span className="hidden sm:inline font-sans text-lg sm:text-xl font-semibold text-brand-black">
                eigenFIT
              </span>
            </Link>
            <ChevronRight className="h-4 w-4 text-zinc-400 sm:hidden" />{" "}
            {/* Separator for mobile */}
            <ChevronRight className="hidden sm:block h-4 w-4 text-zinc-400" />{" "}
            {/* Separator for desktop */}
            <span className="font-sans text-base font-medium text-zinc-600 truncate">
              {pageTitle}
            </span>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <UserNav />
        </div>
      </div>
    </header>
  );
}
