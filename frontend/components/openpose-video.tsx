"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Video, Loader2, Bone } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";

type OpenPoseVideoProps = {
  title: string;
  description: string;
  videoSrc?: string | null;
  profileId?: string | null;
  videoType: "posterior" | "sagittal";
  className?: string;
};

export function OpenPoseVideo({
  title,
  description,
  videoSrc,
  profileId,
  videoType,
  className = "",
}: OpenPoseVideoProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [processedVideoUrl, setProcessedVideoUrl] = useState<string | null>(
    null
  );
  const [taskId, setTaskId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [showOriginal, setShowOriginal] = useState(true);

  // Process the video with MediaPipe Pose
  const processVideo = async () => {
    if (!videoSrc || !profileId) return;

    try {
      setIsLoading(true);
      setError(null);

      // Call the backend API to process the video
      const response = await fetch("/api/openpose-process", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          videoUrl: videoSrc,
          profileId,
          videoType,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to process video");
      }

      const data = await response.json();

      if (data.taskId) {
        setTaskId(data.taskId);
        // Start polling for the processed video
        pollForProcessedVideo(data.taskId);
      } else {
        throw new Error("No task ID returned from server");
      }
    } catch (err) {
      console.error("Error processing video:", err);
      setError(
        err instanceof Error ? err.message : "An unknown error occurred"
      );
      setIsLoading(false);
    }
  };

  // Poll for the processed video
  const pollForProcessedVideo = async (id: string) => {
    try {
      const response = await fetch(`/api/openpose-status?taskId=${id}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to check video status");
      }

      const data = await response.json();

      if (data.processedVideoUrl) {
        // Video is ready
        setProcessedVideoUrl(data.processedVideoUrl);
        setIsLoading(false);
        setShowOriginal(false); // Automatically show the processed video
      } else {
        // Video is still processing, poll again after a delay
        setTimeout(() => pollForProcessedVideo(id), 2000);
      }
    } catch (err) {
      console.error("Error checking video status:", err);
      setError(
        err instanceof Error ? err.message : "An unknown error occurred"
      );
      setIsLoading(false);
    }
  };

  // Toggle between original and processed video
  const toggleVideo = () => {
    setShowOriginal(!showOriginal);
  };

  return (
    <div className="space-y-4">
      <div className="pb-2">
        <h2 className="text-xl font-bold">{title}</h2>
        <p className="text-muted-foreground text-sm">{description}</p>
      </div>

      {/* Video Display */}
      <Card className={`border overflow-hidden ${className}`}>
        <div className="relative">
          {/* Original Video */}
          {videoSrc && showOriginal && (
            <video src={videoSrc} controls className="w-full h-auto" />
          )}

          {/* Processed Video */}
          {processedVideoUrl && !showOriginal && (
            <video src={processedVideoUrl} controls className="w-full h-auto" />
          )}

          {/* Loading State */}
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-black/50">
              <div className="text-center text-white">
                <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
                <p>Processing video...</p>
              </div>
            </div>
          )}

          {/* Empty State */}
          {!videoSrc && !processedVideoUrl && !isLoading && (
            <div className="flex flex-col items-center justify-center p-8 text-center">
              <Bone className="h-12 w-12 text-muted-foreground mb-2" />
              <p className="text-muted-foreground">No video available</p>
            </div>
          )}

          {/* Error State */}
          {error && (
            <div className="p-4 text-center text-red-500">
              <p>Error: {error}</p>
            </div>
          )}
        </div>
      </Card>

      {/* Controls */}
      {videoSrc && (
        <div className="flex flex-wrap gap-2">
          {/* Process Button */}
          {!processedVideoUrl && !isLoading && (
            <Button
              onClick={processVideo}
              variant="default"
              size="sm"
              className="flex items-center gap-1"
            >
              <Bone className="h-4 w-4" />
              Add Pose Overlay
            </Button>
          )}

          {/* Toggle Button */}
          {processedVideoUrl && (
            <Button
              onClick={toggleVideo}
              variant="outline"
              size="sm"
              className="flex items-center gap-1"
            >
              {showOriginal ? "Show Pose Overlay" : "Show Original Video"}
            </Button>
          )}
        </div>
      )}
    </div>
  );
}
