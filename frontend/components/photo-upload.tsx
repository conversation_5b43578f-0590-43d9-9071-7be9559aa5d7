"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Camera, Upload, Loader2 } from "lucide-react"
import Image from "next/image"

type PhotoUploadProps = {
  title: string
  description: string
  imageSrc?: string | null
  onImageChange: (file: File | null, preview: string | null) => void
  illustration?: React.ReactNode
}

export function PhotoUpload({ 
  title, 
  description, 
  imageSrc, 
  onImageChange,
  illustration
}: PhotoUploadProps) {
  const [isUploading, setIsUploading] = useState(false)
  
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null
    if (!file) return
    
    setIsUploading(true)
    
    // Create a preview URL
    const reader = new FileReader()
    reader.onload = (event) => {
      if (event.target?.result) {
        onImageChange(file, event.target.result as string)
        setIsUploading(false)
      }
    }
    reader.readAsDataURL(file)
  }
  
  const handleRemove = () => {
    onImageChange(null, null)
  }
  
  return (
    <div className="space-y-6">
      <div className="pb-6">
        <h2 className="text-2xl font-bold">{title}</h2>
        <p className="text-muted-foreground">{description}</p>
      </div>
      
      {/* Instruction Illustration */}
      {illustration && (
        <div className="border p-4 rounded-md bg-muted/30 flex items-center justify-center h-60">
          {illustration}
        </div>
      )}
      
      {/* Upload Card */}
      <Card className="border-2 border-dashed">
        <div className="p-6 flex flex-col items-center justify-center text-center space-y-4">
          {imageSrc ? (
            <div className="space-y-4">
              <div className="relative h-48 w-full md:w-64 mx-auto">
                <Image 
                  src={imageSrc} 
                  alt={title}
                  fill
                  className="object-contain" 
                />
              </div>
              <div className="flex space-x-2 justify-center">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={handleRemove}
                >
                  Remove
                </Button>
                <label>
                  <Button variant="default" size="sm" asChild>
                    <span className="flex items-center gap-1">
                      <Camera className="h-4 w-4" />
                      Replace photo
                    </span>
                  </Button>
                  <input 
                    type="file" 
                    accept="image/*" 
                    className="sr-only" 
                    onChange={handleFileSelect}
                  />
                </label>
              </div>
            </div>
          ) : (
            <>
              <div className="rounded-full bg-primary/10 p-6">
                <Camera className="h-10 w-10 text-primary" />
              </div>
              <div className="space-y-2">
                <h3 className="font-medium text-lg">Upload Photo</h3>
                <p className="text-sm text-muted-foreground">
                  Take a photo or upload one from your device
                </p>
              </div>
              <label>
                <Button variant="default" asChild>
                  {isUploading ? (
                    <span className="flex items-center gap-2">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Uploading...
                    </span>
                  ) : (
                    <span className="flex items-center gap-2">
                      <Upload className="h-4 w-4" />
                      Select Photo
                    </span>
                  )}
                </Button>
                <input 
                  type="file" 
                  accept="image/*"
                  className="sr-only" 
                  onChange={handleFileSelect}
                  disabled={isUploading}
                />
              </label>
            </>
          )}
        </div>
      </Card>
    </div>
  )
} 