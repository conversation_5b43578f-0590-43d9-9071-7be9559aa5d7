import React from "react";
import Image from "next/image";
import { ImageIcon, Video } from "lucide-react";
import {
  getMediaUrlServer,
  parseStoragePath,
  StoragePath,
} from "@/lib/media-storage";

interface ServerMediaProps {
  storagePath: string | null | undefined;
  type: "image" | "video";
  label?: string;
  className?: string;
  aspectRatio?: "square" | "video" | string;
  showFakeDataLabel?: boolean;
  fallbackText?: string;
}

/**
 * Server component for displaying media from storage paths
 * This component generates signed URLs on the server side
 */
export async function ServerMedia({
  storagePath,
  type,
  label,
  className = "",
  aspectRatio = "square",
  showFakeDataLabel = true,
  fallbackText = "No media available",
}: ServerMediaProps) {
  // Determine aspect ratio class
  const aspectRatioClass =
    aspectRatio === "square"
      ? "aspect-square"
      : aspectRatio === "video"
      ? "aspect-video"
      : aspectRatio;

  // Check if this is fake data
  const isFakeData =
    storagePath &&
    (storagePath.includes("fake-foot-image") ||
      storagePath.includes("fake-running-video"));

  // If no storage path, render placeholder
  if (!storagePath) {
    return (
      <div className="flex flex-col gap-2">
        <div
          className={`relative ${aspectRatioClass} rounded-md overflow-hidden border border-zinc-200 dark:border-zinc-700 bg-zinc-100 dark:bg-zinc-800 flex items-center justify-center ${className}`}
        >
          {type === "image" ? (
            <ImageIcon className="h-10 w-10 text-zinc-400 dark:text-zinc-600" />
          ) : (
            <Video className="h-10 w-10 text-zinc-400 dark:text-zinc-600" />
          )}
        </div>
        {label && (
          <span className="text-xs text-zinc-500 dark:text-zinc-400 text-center">
            {label} (Missing)
          </span>
        )}
      </div>
    );
  }

  // Generate signed URL on the server
  let url: string | null = null;
  let error: string | null = null;

  try {
    // Check if the storage path is valid
    const parsedPath = parseStoragePath(storagePath);
    if (!parsedPath) {
      error = "Invalid storage path format";
      console.error(
        `[ServerMedia] Invalid storage path format: ${storagePath}`
      );
    } else {
      // Generate signed URL
      console.log(`[ServerMedia] Generating signed URL for: ${storagePath}`);
      url = await getMediaUrlServer(storagePath);

      if (!url) {
        error = "Failed to generate signed URL";
        console.error(
          `[ServerMedia] Failed to generate signed URL for: ${storagePath}`
        );

        // Create a fallback URL for development/testing
        if (process.env.NODE_ENV !== "production") {
          if (type === "image") {
            url = "/images/placeholder-image.jpg";
            console.log("[ServerMedia] Using fallback image in development");
          } else if (type === "video") {
            url = "/videos/placeholder-video.mp4";
            console.log("[ServerMedia] Using fallback video in development");
          }
        }
      }
    }
  } catch (err) {
    console.error("[ServerMedia] Error generating signed URL:", err);
    error = err instanceof Error ? err.message : "Unknown error";
  }

  // If error or no URL, render error state
  if (!url || error) {
    return (
      <div className="flex flex-col gap-2">
        <div
          className={`relative ${aspectRatioClass} rounded-md overflow-hidden border border-zinc-200 dark:border-zinc-700 bg-zinc-100 dark:bg-zinc-800 flex items-center justify-center ${className}`}
        >
          {type === "image" ? (
            <ImageIcon className="h-10 w-10 text-zinc-400 dark:text-zinc-600" />
          ) : (
            <Video className="h-10 w-10 text-zinc-400 dark:text-zinc-600" />
          )}
          {error && (
            <div className="absolute bottom-0 left-0 right-0 bg-red-500/70 text-white text-xs py-1 px-2 text-center">
              {error}
            </div>
          )}
        </div>
        {label && (
          <span className="text-xs text-zinc-500 dark:text-zinc-400 text-center">
            {label}
          </span>
        )}
      </div>
    );
  }

  // Render image
  if (type === "image") {
    return (
      <div className="flex flex-col gap-2">
        <div
          className={`relative ${aspectRatioClass} rounded-md overflow-hidden border border-zinc-200 dark:border-zinc-700 ${className}`}
        >
          <div className="w-full h-full relative">
            <Image
              src={url}
              alt={label || "Image"}
              fill
              className="object-cover"
              unoptimized={true} // Skip Next.js image optimization for all URLs
            />
          </div>
          {showFakeDataLabel && isFakeData && (
            <div className="absolute bottom-0 left-0 right-0 bg-amber-500/70 text-white text-xs py-1 px-2 text-center">
              AI Generated
            </div>
          )}
        </div>
        {label && (
          <span className="text-xs text-zinc-500 dark:text-zinc-400 text-center">
            {label}
          </span>
        )}
      </div>
    );
  }

  // Render video
  return (
    <div className="flex flex-col gap-2">
      <div
        className={`relative ${aspectRatioClass} rounded-md overflow-hidden border border-zinc-200 dark:border-zinc-700 ${className}`}
      >
        <video
          src={url}
          controls
          className="w-full h-full object-cover"
          poster={isFakeData ? "/images/video-placeholder.jpg" : undefined}
        />
        {showFakeDataLabel && isFakeData && (
          <div className="absolute bottom-0 left-0 right-0 bg-amber-500/70 text-white text-xs py-1 px-2 text-center">
            AI Generated
          </div>
        )}
      </div>
      {label && (
        <span className="text-xs text-zinc-500 dark:text-zinc-400 text-center">
          {label}
        </span>
      )}
    </div>
  );
}
