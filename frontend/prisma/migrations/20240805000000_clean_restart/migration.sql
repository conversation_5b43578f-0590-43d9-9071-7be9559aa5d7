-- Clean restart migration

-- Create User table
CREATE TABLE IF NOT EXISTS "User" (
  "id" UUID PRIMARY KEY,
  "name" TEXT,
  "email" TEXT NOT NULL UNIQUE,
  "emailVerified" TIMESTAMP WITH TIME ZONE,
  "image" TEXT,
  "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "deletedAt" TIMESTAMP WITH TIME ZONE
);

-- Create RunningProfile table
CREATE TABLE IF NOT EXISTS "RunningProfile" (
  "id" TEXT PRIMARY KEY,
  "name" TEXT NOT NULL,
  "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "isDefault" BOOLEAN NOT NULL DEFAULT false,
  "isCompleted" BOOLEAN NOT NULL DEFAULT false,
  "userId" UUID NOT NULL,
  "age" INTEGER,
  "heightCm" INTEGER,
  "weightKg" INTEGER,
  "gender" TEXT,
  "climate" TEXT,
  "terrain" TEXT,
  "runningGoal" TEXT,
  "previousInjuries" TEXT,
  "previousInjuriesSeverity" TEXT,
  "averageWeeklyKm" TEXT,
  "averagePaceEasyLong" TEXT,
  "averageCadence" INTEGER,
  "runningPower" INTEGER,
  "groundContactTime" INTEGER,
  "verticalOscillationWearable" DOUBLE PRECISION,
  "footImageTopLeftUrl" TEXT,
  "footImageTopRightUrl" TEXT,
  "footImageMedialLeftUrl" TEXT,
  "footImageMedialRightUrl" TEXT,
  "heelToToeLengthLeft" DOUBLE PRECISION,
  "heelToToeLengthRight" DOUBLE PRECISION,
  "forefootWidthLeft" DOUBLE PRECISION,
  "forefootWidthRight" DOUBLE PRECISION,
  "medialArchLengthLeft" DOUBLE PRECISION,
  "medialArchLengthRight" DOUBLE PRECISION,
  "medialArchHeightLeft" DOUBLE PRECISION,
  "medialArchHeightRight" DOUBLE PRECISION,
  "runningVideoPosteriorUrl" TEXT,
  "runningVideoSagittalUrl" TEXT,
  "runningVideoPosteriorTimestamp" DOUBLE PRECISION,
  "runningVideoSagittalTimestamp" DOUBLE PRECISION,
  "pelvicDrop" DOUBLE PRECISION,
  "kneeDrift" DOUBLE PRECISION,
  "footDrift" DOUBLE PRECISION,
  "heelWhip" DOUBLE PRECISION,
  "posteriorStabilityScore" DOUBLE PRECISION,
  "overstride" DOUBLE PRECISION,
  "ankleDorsiflexion" DOUBLE PRECISION,
  "anklePlantarflexion" DOUBLE PRECISION,
  "verticalOscillationVideo" DOUBLE PRECISION,
  "trunkLean" DOUBLE PRECISION,
  "kneeFlexionLoading" DOUBLE PRECISION,
  FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE
);

-- Create ShoeModel table
CREATE TABLE IF NOT EXISTS "ShoeModel" (
  "id" TEXT PRIMARY KEY,
  "brand" TEXT NOT NULL,
  "name" TEXT NOT NULL,
  "fullName" TEXT NOT NULL,
  "modelYear" INTEGER NOT NULL,
  "category" TEXT NOT NULL,
  "price" DOUBLE PRECISION NOT NULL,
  "imageURLs" TEXT[],
  "description" TEXT NOT NULL,
  "pros" TEXT[],
  "cons" TEXT[],
  "rating" DOUBLE PRECISION,
  "numberOfReviews" INTEGER NOT NULL DEFAULT 0,
  "heelStackHeight" DOUBLE PRECISION,
  "drop" DOUBLE PRECISION,
  "midsoleWidth" TEXT,
  "stability" TEXT,
  "midsoleSoftness" INTEGER,
  "forefootFlexibility" INTEGER,
  "midsoleSoftnessInCold" INTEGER,
  "toeboxWidth" TEXT,
  "toeboxHeight" TEXT,
  "type" TEXT,
  "midsoleFormCategory" TEXT,
  "archSupport" TEXT,
  "archType" TEXT,
  "pronation" TEXT,
  "tongueThickness" TEXT,
  "torsionalRigidity" INTEGER,
  "shoeWeightG" INTEGER,
  "rockerGeometry" BOOLEAN,
  "outsoleThickness" DOUBLE PRECISION,
  "terrain" TEXT,
  "medialLateralPosting" BOOLEAN,
  "tongueType" TEXT,
  "insoleThickness" DOUBLE PRECISION,
  "size" DOUBLE PRECISION,
  "widthFit" TEXT,
  "heelCounterStiffness" INTEGER,
  "midsoleWidthForefoot" TEXT,
  "midsoleWidthHeel" TEXT,
  "flexibilityStiffness" INTEGER,
  "stiffnessInCold" INTEGER,
  "tonguePadding" TEXT,
  "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE ("brand", "name", "modelYear")
);

-- Create Recommendation table
CREATE TABLE IF NOT EXISTS "Recommendation" (
  "id" TEXT PRIMARY KEY,
  "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "runningProfileId" TEXT NOT NULL,
  "shoeModel1Id" TEXT,
  "explanation1" TEXT,
  "shoeModel2Id" TEXT,
  "explanation2" TEXT,
  "shoeModel3Id" TEXT,
  "explanation3" TEXT,
  "shoeModel4Id" TEXT,
  "explanation4" TEXT,
  "shoeModel5Id" TEXT,
  "explanation5" TEXT,
  "overallExplanation" TEXT,
  FOREIGN KEY ("runningProfileId") REFERENCES "RunningProfile"("id") ON DELETE CASCADE,
  FOREIGN KEY ("shoeModel1Id") REFERENCES "ShoeModel"("id") ON DELETE SET NULL,
  FOREIGN KEY ("shoeModel2Id") REFERENCES "ShoeModel"("id") ON DELETE SET NULL,
  FOREIGN KEY ("shoeModel3Id") REFERENCES "ShoeModel"("id") ON DELETE SET NULL,
  FOREIGN KEY ("shoeModel4Id") REFERENCES "ShoeModel"("id") ON DELETE SET NULL,
  FOREIGN KEY ("shoeModel5Id") REFERENCES "ShoeModel"("id") ON DELETE SET NULL,
  UNIQUE ("runningProfileId")
);

-- Create trigger function for handling new Supabase users
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public."User" (id, name, email, "createdAt", "updatedAt")
  VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'name', split_part(NEW.email, '@', 1)),
    NEW.email,
    NOW(),
    NOW()
  )
  ON CONFLICT (id) DO NOTHING;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user creation
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

CREATE TRIGGER on_auth_user_created
AFTER INSERT ON auth.users
FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Create trigger function for handling deleted users (soft delete)
CREATE OR REPLACE FUNCTION public.handle_deleted_user()
RETURNS TRIGGER AS $$
BEGIN
  -- Soft delete by setting deletedAt timestamp
  UPDATE public."User"
  SET "deletedAt" = NOW()
  WHERE id = OLD.id;

  RETURN OLD;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for user deletion
DROP TRIGGER IF EXISTS on_auth_user_deleted ON auth.users;

CREATE TRIGGER on_auth_user_deleted
AFTER DELETE ON auth.users
FOR EACH ROW EXECUTE FUNCTION public.handle_deleted_user();

-- Enable Row Level Security on User table
ALTER TABLE public."User" ENABLE ROW LEVEL SECURITY;

-- Create policy to prevent access to deleted users
CREATE POLICY user_not_deleted ON public."User"
  FOR ALL
  TO authenticated
  USING ("deletedAt" IS NULL);

-- Create policy for RunningProfile to prevent access to profiles of deleted users
ALTER TABLE public."RunningProfile" ENABLE ROW LEVEL SECURITY;

CREATE POLICY profile_user_not_deleted ON public."RunningProfile"
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public."User" u
      WHERE u.id = "userId"
      AND u."deletedAt" IS NULL
    )
  );
