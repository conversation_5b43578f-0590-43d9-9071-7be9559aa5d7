-- Fix for Recommendation model column names
-- This migration renames matchScore to overallMatchScore and matchReasons to keyStrengths
-- if the old columns exist and the new ones don't

DO $$
BEGIN
  -- Check if matchScore exists but overallMatchScore doesn't
  IF EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_name = 'Recommendation' AND column_name = 'matchScore'
  ) AND NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_name = 'Recommendation' AND column_name = 'overallMatchScore'
  ) THEN
    -- Rename matchScore to overallMatchScore
    ALTER TABLE "Recommendation" RENAME COLUMN "matchScore" TO "overallMatchScore";
  END IF;

  -- Check if matchReasons exists but keyStrengths doesn't
  IF EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_name = 'Recommendation' AND column_name = 'matchReasons'
  ) AND NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_name = 'Recommendation' AND column_name = 'keyStrengths'
  ) THEN
    -- Rename matchReasons to keyStrengths
    ALTER TABLE "Recommendation" RENAME COLUMN "matchReasons" TO "keyStrengths";
  END IF;

  -- Add missing columns if they don't exist
  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_name = 'Recommendation' AND column_name = 'rank'
  ) THEN
    ALTER TABLE "Recommendation" ADD COLUMN "rank" INTEGER NOT NULL DEFAULT 0;
  END IF;

  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_name = 'Recommendation' AND column_name = 'updatedAt'
  ) THEN
    ALTER TABLE "Recommendation" ADD COLUMN "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP;
  END IF;

  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_name = 'Recommendation' AND column_name = 'stabilityScore'
  ) THEN
    ALTER TABLE "Recommendation" ADD COLUMN "stabilityScore" DOUBLE PRECISION;
  END IF;

  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_name = 'Recommendation' AND column_name = 'cushioningScore'
  ) THEN
    ALTER TABLE "Recommendation" ADD COLUMN "cushioningScore" DOUBLE PRECISION;
  END IF;

  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_name = 'Recommendation' AND column_name = 'fitScore'
  ) THEN
    ALTER TABLE "Recommendation" ADD COLUMN "fitScore" DOUBLE PRECISION;
  END IF;

  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_name = 'Recommendation' AND column_name = 'performanceScore'
  ) THEN
    ALTER TABLE "Recommendation" ADD COLUMN "performanceScore" DOUBLE PRECISION;
  END IF;

  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_name = 'Recommendation' AND column_name = 'terrainScore'
  ) THEN
    ALTER TABLE "Recommendation" ADD COLUMN "terrainScore" DOUBLE PRECISION;
  END IF;

  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_name = 'Recommendation' AND column_name = 'stabilityExplanation'
  ) THEN
    ALTER TABLE "Recommendation" ADD COLUMN "stabilityExplanation" TEXT;
  END IF;

  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_name = 'Recommendation' AND column_name = 'cushioningExplanation'
  ) THEN
    ALTER TABLE "Recommendation" ADD COLUMN "cushioningExplanation" TEXT;
  END IF;

  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_name = 'Recommendation' AND column_name = 'fitExplanation'
  ) THEN
    ALTER TABLE "Recommendation" ADD COLUMN "fitExplanation" TEXT;
  END IF;

  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_name = 'Recommendation' AND column_name = 'performanceExplanation'
  ) THEN
    ALTER TABLE "Recommendation" ADD COLUMN "performanceExplanation" TEXT;
  END IF;

  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_name = 'Recommendation' AND column_name = 'terrainExplanation'
  ) THEN
    ALTER TABLE "Recommendation" ADD COLUMN "terrainExplanation" TEXT;
  END IF;

  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_name = 'Recommendation' AND column_name = 'overallExplanation'
  ) THEN
    ALTER TABLE "Recommendation" ADD COLUMN "overallExplanation" TEXT NOT NULL DEFAULT 'No explanation available';
  END IF;

  IF NOT EXISTS (
    SELECT FROM information_schema.columns
    WHERE table_name = 'Recommendation' AND column_name = 'keyWeaknesses'
  ) THEN
    ALTER TABLE "Recommendation" ADD COLUMN "keyWeaknesses" TEXT[];
  END IF;
END $$;
