-- First, we need to update the RunningProfile table to remove the foreign key constraint
ALTER TABLE public."RunningProfile"
DROP CONSTRAINT IF EXISTS "RunningProfile_userId_fkey";

-- Now, alter the User table to change the id column type to UUID
ALTER TABLE public."User"
ALTER COLUMN id TYPE UUID USING id::UUID;

-- Update the RunningProfile table to change the userId column type to UUID
ALTER TABLE public."RunningProfile"
ALTER COLUMN "userId" TYPE UUID USING "userId"::UUID;

-- Re-add the foreign key constraint to RunningProfile
ALTER TABLE public."RunningProfile"
ADD CONSTRAINT "RunningProfile_userId_fkey"
FOREIGN KEY ("userId")
REFERENCES public."User"(id)
ON DELETE CASCADE;

-- Now add the foreign key constraint to User
ALTER TABLE public."User"
DROP CONSTRAINT IF EXISTS "User_id_fkey";

ALTER TABLE public."User"
ADD CONSTRAINT "User_id_fkey"
FOREIGN KEY (id)
REFERENCES auth.users(id)
ON DELETE CASCADE;
