"use client"

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { mockRunningMetrics } from "@/lib/mock-data"
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Calendar, Footprints, LineChart, BarChart3, Activity, TrendingUp, LucideIcon, Target } from "lucide-react"

function MetricCard({ title, value, icon: Icon, description }: {
  title: string
  value: string
  icon: LucideIcon
  description: string
}) {
  return (
    <Card className="bg-zinc-50 dark:bg-zinc-800/60 border border-zinc-200 dark:border-zinc-700 shadow-sm rounded-lg hover:border-zinc-300 dark:hover:border-zinc-600 transition-colors">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 pt-4 px-4">
        <CardTitle className="text-xs font-medium text-zinc-500 dark:text-zinc-400 uppercase tracking-wider font-input">{title}</CardTitle>
        <Icon className="h-4 w-4 text-zinc-400 dark:text-zinc-500" />
      </CardHeader>
      <CardContent className="pb-4 px-4">
        <div className="text-2xl font-bold font-jetbrains text-zinc-900 dark:text-zinc-100 tabular-nums">{value}</div>
        <p className="text-xs text-zinc-500 dark:text-zinc-400 font-input pt-1">{description}</p>
      </CardContent>
    </Card>
  )
}

export default function AnalyticsPage() {
  // Calculate summary metrics
  const totalDistance = mockRunningMetrics.reduce((sum, metric) => sum + metric.distance, 0)
  const averagePace = mockRunningMetrics.reduce((sum, metric) => sum + metric.pace, 0) / mockRunningMetrics.length
  const totalRuns = mockRunningMetrics.length
  const averageCadence = Math.round(
    mockRunningMetrics.reduce((sum, metric) => sum + metric.cadence, 0) / mockRunningMetrics.length
  )

  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <MetricCard
          title="Total Distance"
          value={`${totalDistance.toFixed(1)} km`}
          icon={Footprints}
          description="Last 30 days"
        />
        <MetricCard
          title="Average Pace"
          value={`${averagePace.toFixed(2)} min/km`}
          icon={TrendingUp}
          description="Last 30 days"
        />
        <MetricCard
          title="Total Runs"
          value={totalRuns.toString()}
          icon={Calendar}
          description="Last 30 days"
        />
        <MetricCard
          title="Avg. Cadence"
          value={`${averageCadence} spm`}
          icon={Activity}
          description="Steps per minute"
        />
      </div>

      <Tabs defaultValue="distance">
        <TabsList className="grid w-full grid-cols-3 bg-zinc-100 dark:bg-zinc-800">
          <TabsTrigger value="distance">Distance</TabsTrigger>
          <TabsTrigger value="pace">Pace</TabsTrigger>
          <TabsTrigger value="elevation">Elevation</TabsTrigger>
        </TabsList>
        <TabsContent value="distance" className="mt-4">
          <Card className="bg-zinc-50 dark:bg-zinc-800/60 border border-zinc-200 dark:border-zinc-700 shadow-sm rounded-lg">
            <CardHeader className="border-b border-zinc-200 dark:border-zinc-700">
              <CardTitle className="font-sans text-lg font-medium text-zinc-900 dark:text-zinc-100">Distance Over Time</CardTitle>
              <CardDescription className="text-sm text-zinc-500 dark:text-zinc-400 pt-1">Your running distance trends for the past 30 days</CardDescription>
            </CardHeader>
            <CardContent className="h-[400px] flex flex-col items-center justify-center text-center bg-white dark:bg-zinc-700/40">
              <LineChart className="h-16 w-16 text-zinc-400 dark:text-zinc-600 mb-4" />
              <span className="text-sm text-zinc-500 dark:text-zinc-400 font-input">Distance chart will appear here</span>
              <span className="text-xs text-zinc-400 dark:text-zinc-500 font-input mt-1">(Chart component not implemented)</span>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="pace" className="mt-4">
          <Card className="bg-zinc-50 dark:bg-zinc-800/60 border border-zinc-200 dark:border-zinc-700 shadow-sm rounded-lg">
            <CardHeader className="border-b border-zinc-200 dark:border-zinc-700">
              <CardTitle className="font-sans text-lg font-medium text-zinc-900 dark:text-zinc-100">Pace Analysis</CardTitle>
              <CardDescription className="text-sm text-zinc-500 dark:text-zinc-400 pt-1">Your running pace trends and improvements</CardDescription>
            </CardHeader>
            <CardContent className="h-[400px] flex flex-col items-center justify-center text-center bg-white dark:bg-zinc-700/40">
              <BarChart3 className="h-16 w-16 text-zinc-400 dark:text-zinc-600 mb-4" />
              <span className="text-sm text-zinc-500 dark:text-zinc-400 font-input">Pace chart will appear here</span>
              <span className="text-xs text-zinc-400 dark:text-zinc-500 font-input mt-1">(Chart component not implemented)</span>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="elevation" className="mt-4">
          <Card className="bg-zinc-50 dark:bg-zinc-800/60 border border-zinc-200 dark:border-zinc-700 shadow-sm rounded-lg">
            <CardHeader className="border-b border-zinc-200 dark:border-zinc-700">
              <CardTitle className="font-sans text-lg font-medium text-zinc-900 dark:text-zinc-100">Elevation Profile</CardTitle>
              <CardDescription className="text-sm text-zinc-500 dark:text-zinc-400 pt-1">Your elevation gains and training intensity</CardDescription>
            </CardHeader>
            <CardContent className="h-[400px] flex flex-col items-center justify-center text-center bg-white dark:bg-zinc-700/40">
              <TrendingUp className="h-16 w-16 text-zinc-400 dark:text-zinc-600 mb-4" />
              <span className="text-sm text-zinc-500 dark:text-zinc-400 font-input">Elevation chart will appear here</span>
              <span className="text-xs text-zinc-400 dark:text-zinc-500 font-input mt-1">(Chart component not implemented)</span>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
} 