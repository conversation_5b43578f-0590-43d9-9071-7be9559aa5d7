"use client";
import React, { useCallback } from "react";
import { useEffect, useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from "@/components/ui/alert";
import { 
  ChevronLeft, 
  RefreshCw, 
  Star, 
  Info, 
  CheckCircle,
  ExternalLink,
  ArrowRight,
  Loader2
} from "lucide-react";
import { Progress } from "@/components/ui/progress";
import { ShoeRecommendation, RunningProfile, ShoeModel } from "@/lib/types";

export default function RecommendationsPage({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = React.use(params);
  const { id } = resolvedParams;
  const [profile, setProfile] = useState<RunningProfile | null>(null);
  const [recommendation, setRecommendation] = useState<ShoeRecommendation | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const fetchRecommendations = useCallback(async (regenerate = false) => {
    try {
      if (regenerate) {
        setRefreshing(true);
        
        // Delete existing recommendations to force regeneration
        await fetch(`/api/running-profiles/${id}/recommendations`, {
          method: "DELETE",
        });
      }
      
      // Get profile info 
      const profileResponse = await fetch(`/api/running-profiles/${id}`);
      
      if (!profileResponse.ok) {
        const errorData = await profileResponse.json();
        throw new Error(errorData.error || "Failed to fetch running profile");
      }
      
      const profileData = await profileResponse.json();
      setProfile(profileData.runningProfile);
      
      // Get recommendations
      const recommendationsResponse = await fetch(`/api/running-profiles/${id}/recommendations`);
      
      if (!recommendationsResponse.ok) {
        const errorData = await recommendationsResponse.json();
        throw new Error(errorData.error || "Failed to fetch recommendations");
      }
      
      const data = await recommendationsResponse.json();
      setRecommendation(data.recommendation || null);
    } catch (err) {
      console.error("Error fetching recommendations:", err);
      setError(err instanceof Error ? err.message : "Failed to load recommendations");
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [id]);

  useEffect(() => {
    fetchRecommendations();
  }, [id, fetchRecommendations]);

  const handleRegenerateRecommendations = () => {
    fetchRecommendations(true);
  };

  if (loading) {
    return (
      <div className="flex min-h-screen bg-muted/40">
        <main className="flex-1 p-6 md:p-8 flex justify-center items-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-lg font-medium">Loading recommendations...</p>
          </div>
        </main>
      </div>
    );
  }

  if (error || !profile) {
    return (
      <div className="flex min-h-screen bg-muted/40">
        <main className="flex-1 p-6 md:p-8">
          <div className="rounded-md bg-destructive/10 p-4 text-destructive">
            <p>{error || "Profile not found"}</p>
          </div>
          <Link href="/running-profiles">
            <Button variant="ghost" className="mt-4">
              <ChevronLeft className="mr-2 h-4 w-4" />
              Back to Profiles
            </Button>
          </Link>
        </main>
      </div>
    );
  }

  // Create an array of shoe models and explanations
  const recommendedShoes = [];
  if (recommendation?.shoeModel1) {
    recommendedShoes.push({
      id: recommendation.shoeModel1Id,
      shoeModel: recommendation.shoeModel1,
      explanation: recommendation.explanation1 || "",
      position: 1
    });
  }
  if (recommendation?.shoeModel2) {
    recommendedShoes.push({
      id: recommendation.shoeModel2Id,
      shoeModel: recommendation.shoeModel2,
      explanation: recommendation.explanation2 || "",
      position: 2
    });
  }
  if (recommendation?.shoeModel3) {
    recommendedShoes.push({
      id: recommendation.shoeModel3Id,
      shoeModel: recommendation.shoeModel3,
      explanation: recommendation.explanation3 || "",
      position: 3
    });
  }
  if (recommendation?.shoeModel4) {
    recommendedShoes.push({
      id: recommendation.shoeModel4Id,
      shoeModel: recommendation.shoeModel4,
      explanation: recommendation.explanation4 || "",
      position: 4
    });
  }
  if (recommendation?.shoeModel5) {
    recommendedShoes.push({
      id: recommendation.shoeModel5Id,
      shoeModel: recommendation.shoeModel5,
      explanation: recommendation.explanation5 || "",
      position: 5
    });
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Link href={`/running-profiles/${id}`}>
          <Button variant="ghost" size="icon">
            <ChevronLeft className="h-5 w-5" />
            <span className="sr-only">Back</span>
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Recommendations for {profile.name}
          </h1>
          <p className="text-muted-foreground">
            Personalized shoe recommendations based on your running profile
          </p>
        </div>
        <div className="ml-auto">
          <Button
            variant="outline"
            onClick={handleRegenerateRecommendations}
            disabled={refreshing}
            className="flex items-center gap-2"
          >
            {refreshing ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
            {refreshing ? "Regenerating..." : "Regenerate"}
          </Button>
        </div>
      </div>
      
      {recommendedShoes.length === 0 ? (
        <Alert>
          <Info className="h-5 w-5" />
          <AlertTitle>No recommendations available</AlertTitle>
          <AlertDescription>
            We couldn&apos;t generate recommendations based on your profile. 
            Please make sure your profile is complete with foot type, pronation, 
            and running preferences.
          </AlertDescription>
        </Alert>
      ) : (
        <div className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {recommendedShoes.map((rec) => (
              <Card key={rec.id} className="flex flex-col">
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-lg">{rec.shoeModel.fullName || `${rec.shoeModel.brand} ${rec.shoeModel.name}`}</CardTitle>
                      <CardDescription>
                        {rec.shoeModel.category} • {rec.shoeModel.type}
                      </CardDescription>
                    </div>
                    <div className="flex items-center bg-primary/10 px-2 py-1 rounded-full text-xs font-medium text-primary">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      #{rec.position} Recommendation
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="flex-grow pb-0">
                  <div className="aspect-square relative mb-4 bg-muted rounded-md overflow-hidden">
                    {rec.shoeModel.imageURLs && rec.shoeModel.imageURLs.length > 0 ? (
                      <Image
                        src={rec.shoeModel.imageURLs[0]}
                        alt={rec.shoeModel.fullName || rec.shoeModel.name}
                        fill
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                        className="object-cover"
                      />
                    ) : (
                      <div className="absolute inset-0 flex items-center justify-center text-muted-foreground">
                        No image available
                      </div>
                    )}
                  </div>
                  
                  <div className="space-y-3">
                    <div>
                      <h4 className="text-sm font-medium mb-1">Why this shoe matches your profile:</h4>
                      <p className="text-sm text-muted-foreground">{rec.explanation}</p>
                    </div>
                    
                    <div className="flex justify-between text-sm pt-2">
                      <div>
                        <span className="font-medium">Price:</span> ${rec.shoeModel.price}
                      </div>
                      {rec.shoeModel.rating && (
                        <div className="flex items-center">
                          <Star className="h-4 w-4 text-yellow-500 mr-1 fill-yellow-500" />
                          <span>{rec.shoeModel.rating.toFixed(1)}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="pt-4">
                  <Button 
                    onClick={() => router.push(`/shoe-models/${rec.id}`)}
                    className="w-full"
                  >
                    <span>View Details</span>
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
          
          <div className="flex justify-between">
            <Link href={`/running-profiles/${id}`}>
              <Button variant="outline">
                <ChevronLeft className="mr-2 h-4 w-4" />
                Back to Profile
              </Button>
            </Link>
            <Link href="/shop" className="flex items-center gap-1 text-sm text-muted-foreground hover:text-foreground">
              <span>Browse all shoes</span>
              <ExternalLink className="h-3 w-3" />
            </Link>
          </div>
        </div>
      )}
    </div>
  );
}
