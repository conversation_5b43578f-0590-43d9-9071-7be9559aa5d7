"use client";

import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useRouter } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Loader2, Eye, EyeOff, KeyRound, Mail, Trash2 } from "lucide-react";

export default function SettingsPage() {
  const { user, signOut } = useAuth(); // Get user and signOut from Supabase Auth context
  const router = useRouter();

  // === State for Modals ===
  const [isPasswordDialogOpen, setIsPasswordDialogOpen] = useState(false);
  const [isEmailDialogOpen, setIsEmailDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // === State for Change Password ===
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [passwordError, setPasswordError] = useState("");
  const [passwordSuccess, setPasswordSuccess] = useState("");
  const [isPasswordLoading, setIsPasswordLoading] = useState(false);

  // === State for Change Email ===
  const [newEmail, setNewEmail] = useState("");
  const [emailPassword, setEmailPassword] = useState("");
  const [showEmailPassword, setShowEmailPassword] = useState(false);
  const [emailError, setEmailError] = useState("");
  const [emailSuccess, setEmailSuccess] = useState("");
  const [isEmailLoading, setIsEmailLoading] = useState(false);

  // === State for Delete Account ===
  const [deletePassword, setDeletePassword] = useState("");
  const [showDeletePassword, setShowDeletePassword] = useState(false);
  const [deleteError, setDeleteError] = useState("");
  const [isDeleteLoading, setIsDeleteLoading] = useState(false);

  // --- Helper function to reset password form state ---
  const resetPasswordForm = () => {
    setCurrentPassword("");
    setNewPassword("");
    setConfirmPassword("");
    setPasswordError("");
    setPasswordSuccess("");
    setShowCurrentPassword(false);
    setShowNewPassword(false);
    setShowConfirmPassword(false);
    setIsPasswordLoading(false);
    setIsPasswordDialogOpen(false);
  };

  // --- Helper function to reset email form state ---
  const resetEmailForm = () => {
    setNewEmail("");
    setEmailPassword("");
    setEmailError("");
    setEmailSuccess("");
    setShowEmailPassword(false);
    setIsEmailLoading(false);
    setIsEmailDialogOpen(false);
  };

  // --- Helper function to reset delete form state ---
  const resetDeleteForm = () => {
    setDeletePassword("");
    setDeleteError("");
    setShowDeletePassword(false);
    setIsDeleteLoading(false);
    setIsDeleteDialogOpen(false);
  };

  // --- Password Change Handler ---
  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault();
    setPasswordError("");
    setPasswordSuccess("");

    if (newPassword !== confirmPassword) {
      setPasswordError("New passwords do not match");
      return;
    }
    if (newPassword.length < 8) {
      setPasswordError("New password must be at least 8 characters");
      return;
    }

    setIsPasswordLoading(true);
    try {
      const response = await fetch("/api/user/supabase-change-password", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ currentPassword, newPassword }),
      });
      const data = await response.json();
      if (!response.ok)
        throw new Error(data.error || "Failed to change password");
      setPasswordSuccess("Password successfully updated!");
      setTimeout(resetPasswordForm, 2000); // Close dialog after success
    } catch (error) {
      setPasswordError(
        error instanceof Error ? error.message : "An error occurred"
      );
    } finally {
      setIsPasswordLoading(false);
    }
  };

  // --- Email Change Handler ---
  const handleEmailChange = async (e: React.FormEvent) => {
    e.preventDefault();
    setEmailError("");
    setEmailSuccess("");

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(newEmail)) {
      setEmailError("Please enter a valid email address");
      return;
    }

    setIsEmailLoading(true);
    try {
      const response = await fetch("/api/user/supabase-change-email", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ newEmail, password: emailPassword }),
      });
      const data = await response.json();
      if (!response.ok) throw new Error(data.error || "Failed to change email");

      // Email is updated in Supabase Auth
      // We'll need to update the user's email in Supabase Auth in the API route
      setEmailSuccess("Email successfully updated! Session refreshed.");
      setTimeout(resetEmailForm, 2000); // Close dialog after success
    } catch (error) {
      setEmailError(
        error instanceof Error ? error.message : "An error occurred"
      );
    } finally {
      setIsEmailLoading(false);
    }
  };

  // --- Account Deletion Handler ---
  const handleAccountDeletion = async () => {
    setDeleteError("");
    setIsDeleteLoading(true);
    try {
      const response = await fetch("/api/user/supabase-delete-account", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ password: deletePassword }),
      });
      const data = await response.json();

      if (!response.ok) {
        console.error("Account deletion error:", data);
        throw new Error(data.error || "Failed to delete account");
      }

      // Sign out and redirect on success
      await signOut();
      router.push("/"); // Redirect to home page
    } catch (error) {
      console.error("Account deletion error:", error);
      setDeleteError(
        error instanceof Error ? error.message : "An error occurred"
      );
      setIsDeleteLoading(false); // Keep dialog open on error
    }
    // Don't set loading false on success, redirect handles it
  };

  // Input field component for consistency
  const ThemedInput = ({
    id,
    label,
    type,
    placeholder,
    value,
    onChange,
    disabled,
    error,
    children,
    showPasswordToggle,
  }: any) => (
    <div className="space-y-1.5">
      <Label
        htmlFor={id}
        className="text-xs font-medium text-zinc-600 dark:text-zinc-400"
      >
        {label}
      </Label>
      <div className="relative">
        <Input
          id={id}
          name={id}
          type={type}
          placeholder={placeholder}
          value={value}
          onChange={onChange}
          disabled={disabled}
          className={`bg-white dark:bg-zinc-800 border-zinc-300 dark:border-zinc-700 focus:border-zinc-500 dark:focus:border-zinc-500 text-zinc-900 dark:text-zinc-100 placeholder:text-zinc-400 dark:placeholder:text-zinc-500 ring-offset-white dark:ring-offset-zinc-950 focus-visible:ring-zinc-950 dark:focus-visible:ring-zinc-300 h-9 text-sm ${
            showPasswordToggle ? "pr-10" : ""
          }`}
          autoComplete={type === "password" ? "new-password" : "off"}
        />
        {children} {/* For password visibility toggle */}
      </div>
      {error && (
        <p className="text-red-600 dark:text-red-500 text-xs mt-1 font-input">
          {error}
        </p>
      )}
    </div>
  );

  // Password visibility toggle button
  const PasswordToggle = ({
    show,
    setShow,
    disabled,
  }: {
    show: boolean;
    setShow: (show: boolean) => void;
    disabled?: boolean;
  }) => (
    <Button
      type="button"
      variant="ghost"
      size="sm"
      className="absolute right-1.5 top-1/2 -translate-y-1/2 h-7 w-7 p-0 text-zinc-400 dark:text-zinc-500 hover:text-zinc-600 dark:hover:text-zinc-300 hover:bg-zinc-100 dark:hover:bg-zinc-700/50"
      onClick={() => setShow(!show)}
      disabled={disabled}
      tabIndex={-1} // Prevent tabbing to it
    >
      {show ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
      <span className="sr-only">
        {show ? "Hide password" : "Show password"}
      </span>
    </Button>
  );

  // Themed Submit Button (Now uses Brand Green)
  const SubmitButton = ({
    isLoading,
    loadingText,
    defaultText,
    fullWidth = true,
  }: any) => (
    <Button
      type="submit"
      className={`${
        fullWidth ? "w-full" : ""
      } bg-brand-green text-off-white hover:bg-brand-green-light font-sans text-sm h-9 group relative overflow-hidden transition-colors duration-300 disabled:opacity-60`}
      disabled={isLoading}
    >
      {isLoading ? (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          {loadingText || "Processing..."}
        </>
      ) : (
        <span className="relative z-10">{defaultText || "Submit"} &gt;</span>
      )}
    </Button>
  );

  return (
    <div className="space-y-8">
      {/* Title removed, now handled in Header */}

      {/* Settings Cards Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* Profile Information Card (Display Only) */}
        <Card className="bg-zinc-50 dark:bg-zinc-800/60 border border-zinc-200 dark:border-zinc-700 shadow-sm rounded-lg">
          <CardHeader className="pb-4">
            <CardTitle className="font-sans text-lg font-medium flex items-center gap-2 text-zinc-900 dark:text-zinc-100">
              <Mail className="h-5 w-5 text-zinc-500 dark:text-zinc-400" />{" "}
              Email
            </CardTitle>
            <CardDescription className="text-sm text-zinc-500 dark:text-zinc-400 pt-1">
              Your registered email address.
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-0 bg-white dark:bg-zinc-800/30 p-4 rounded-b-lg border-t border-zinc-200 dark:border-zinc-700">
            <p className="text-sm font-medium text-zinc-700 dark:text-zinc-300 font-input break-words">
              {user?.email || "Loading..."}
            </p>
          </CardContent>
        </Card>

        {/* Change Password Card */}
        <Card className="bg-zinc-50 dark:bg-zinc-800/60 border border-zinc-200 dark:border-zinc-700 shadow-sm rounded-lg">
          <CardHeader className="pb-4">
            <CardTitle className="font-sans text-lg font-medium flex items-center gap-2 text-zinc-900 dark:text-zinc-100">
              <KeyRound className="h-5 w-5 text-zinc-500 dark:text-zinc-400" />{" "}
              Security
            </CardTitle>
            <CardDescription className="text-sm text-zinc-500 dark:text-zinc-400 pt-1">
              Manage your account security.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3 pt-0 border-t border-zinc-200 dark:border-zinc-700 p-4 rounded-b-lg bg-white dark:bg-zinc-800/30">
            <Dialog
              open={isPasswordDialogOpen}
              onOpenChange={(open) => {
                setIsPasswordDialogOpen(open);
                if (!open) resetPasswordForm();
              }}
            >
              <DialogTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full justify-start border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-700/50 hover:bg-zinc-100 dark:hover:bg-zinc-700 hover:text-zinc-900 dark:hover:text-zinc-100 text-zinc-700 dark:text-zinc-300 h-9 text-sm font-normal"
                >
                  Change Password...
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[425px] bg-white dark:bg-zinc-900 border-zinc-200 dark:border-zinc-700 text-zinc-900 dark:text-zinc-100 rounded-lg">
                <DialogHeader>
                  <DialogTitle className="font-sans text-lg font-medium">
                    Change Password
                  </DialogTitle>
                  <DialogDescription className="text-zinc-500 dark:text-zinc-400 text-sm pt-1">
                    Enter your current and new password.
                  </DialogDescription>
                </DialogHeader>
                <form
                  onSubmit={handlePasswordChange}
                  className="space-y-4 py-2"
                >
                  {passwordError && (
                    <p className="text-red-600 dark:text-red-500 text-xs font-input bg-red-50 dark:bg-red-900/10 p-2 rounded-sm">
                      {passwordError}
                    </p>
                  )}
                  {passwordSuccess && (
                    <p className="text-emerald-600 dark:text-emerald-400 text-sm font-medium font-input">
                      {passwordSuccess}
                    </p>
                  )}
                  <ThemedInput
                    id="currentPassword"
                    label="Current Password"
                    type={showCurrentPassword ? "text" : "password"}
                    placeholder=">********"
                    value={currentPassword}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                      setCurrentPassword(e.target.value)
                    }
                    disabled={isPasswordLoading}
                    error={null} // Error handled above form
                    showPasswordToggle={true}
                  >
                    <PasswordToggle
                      show={showCurrentPassword}
                      setShow={setShowCurrentPassword}
                      disabled={isPasswordLoading}
                    />
                  </ThemedInput>
                  <ThemedInput
                    id="newPassword"
                    label="New Password (min 8 chars)"
                    type={showNewPassword ? "text" : "password"}
                    placeholder=">********"
                    value={newPassword}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                      setNewPassword(e.target.value)
                    }
                    disabled={isPasswordLoading}
                    error={null}
                    showPasswordToggle={true}
                  >
                    <PasswordToggle
                      show={showNewPassword}
                      setShow={setShowNewPassword}
                      disabled={isPasswordLoading}
                    />
                  </ThemedInput>
                  <ThemedInput
                    id="confirmPassword"
                    label="Confirm New Password"
                    type={showConfirmPassword ? "text" : "password"}
                    placeholder=">********"
                    value={confirmPassword}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                      setConfirmPassword(e.target.value)
                    }
                    disabled={isPasswordLoading}
                    error={null}
                    showPasswordToggle={true}
                  >
                    <PasswordToggle
                      show={showConfirmPassword}
                      setShow={setShowConfirmPassword}
                      disabled={isPasswordLoading}
                    />
                  </ThemedInput>
                  <DialogFooter className="pt-2">
                    <SubmitButton
                      isLoading={isPasswordLoading}
                      loadingText="Updating..."
                      defaultText="Update Password"
                    />
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>
          </CardContent>
        </Card>

        {/* Change Email Card */}
        <Card className="bg-zinc-50 dark:bg-zinc-800/60 border border-zinc-200 dark:border-zinc-700 shadow-sm rounded-lg">
          <CardHeader className="pb-4">
            <CardTitle className="font-sans text-lg font-medium flex items-center gap-2 text-zinc-900 dark:text-zinc-100">
              <Mail className="h-5 w-5 text-zinc-500 dark:text-zinc-400" />{" "}
              Change Email
            </CardTitle>
            <CardDescription className="text-sm text-zinc-500 dark:text-zinc-400 pt-1">
              Update the email associated with your account.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Dialog
              open={isEmailDialogOpen}
              onOpenChange={(open) => {
                setIsEmailDialogOpen(open);
                if (!open) resetEmailForm();
              }}
            >
              <DialogTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full justify-start border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-700/50 hover:bg-zinc-100 dark:hover:bg-zinc-700 hover:text-zinc-900 dark:hover:text-zinc-100 text-zinc-700 dark:text-zinc-300 h-9 text-sm font-normal"
                >
                  Change Email Address...
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[425px] bg-white dark:bg-zinc-900 border-zinc-200 dark:border-zinc-700 text-zinc-900 dark:text-zinc-100 rounded-lg">
                <DialogHeader>
                  <DialogTitle className="font-sans text-lg font-medium">
                    Change Email Address
                  </DialogTitle>
                  <DialogDescription className="text-zinc-500 dark:text-zinc-400 text-sm pt-1">
                    Enter your new email and current password.
                  </DialogDescription>
                </DialogHeader>
                <form onSubmit={handleEmailChange} className="space-y-4 py-2">
                  {emailError && (
                    <p className="text-red-600 dark:text-red-500 text-xs font-input bg-red-50 dark:bg-red-900/10 p-2 rounded-sm">
                      {emailError}
                    </p>
                  )}
                  {emailSuccess && (
                    <p className="text-emerald-600 dark:text-emerald-400 text-sm font-medium font-input">
                      {emailSuccess}
                    </p>
                  )}
                  <ThemedInput
                    id="newEmail"
                    label="New Email Address"
                    type="email"
                    placeholder="<EMAIL>"
                    value={newEmail}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                      setNewEmail(e.target.value)
                    }
                    disabled={isEmailLoading}
                    error={null}
                  />
                  <ThemedInput
                    id="emailPassword"
                    label="Current Password"
                    type={showEmailPassword ? "text" : "password"}
                    placeholder=">********"
                    value={emailPassword}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                      setEmailPassword(e.target.value)
                    }
                    disabled={isEmailLoading}
                    error={null}
                    showPasswordToggle={true}
                  >
                    <PasswordToggle
                      show={showEmailPassword}
                      setShow={setShowEmailPassword}
                      disabled={isEmailLoading}
                    />
                  </ThemedInput>
                  <DialogFooter className="pt-2">
                    <SubmitButton
                      isLoading={isEmailLoading}
                      loadingText="Updating..."
                      defaultText="Update Email"
                    />
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>
          </CardContent>
        </Card>

        {/* Delete Account Card */}
        <Card className="border-red-400 dark:border-red-600/70 bg-red-50 dark:bg-red-900/20 shadow-sm rounded-lg">
          <CardHeader className="pb-4">
            <CardTitle className="font-sans text-lg font-medium flex items-center gap-2 text-red-700 dark:text-red-400">
              <Trash2 className="h-5 w-5" /> Danger Zone
            </CardTitle>
            <CardDescription className="text-sm text-red-600 dark:text-red-400/80 pt-1">
              Irreversible account actions.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3 pt-0 border-t border-red-300 dark:border-red-600/50 p-4 rounded-b-lg bg-red-100/50 dark:bg-red-900/10">
            <AlertDialog
              open={isDeleteDialogOpen}
              onOpenChange={(open) => {
                setIsDeleteDialogOpen(open);
                if (!open) resetDeleteForm();
              }}
            >
              <AlertDialogTrigger asChild>
                <Button
                  variant="destructive"
                  className="w-full justify-start bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-600 h-9 text-sm font-normal"
                >
                  Delete Account...
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent className="sm:max-w-[425px] bg-white dark:bg-zinc-900 border-red-300 dark:border-red-700 text-zinc-900 dark:text-zinc-100 rounded-lg">
                <AlertDialogHeader>
                  <AlertDialogTitle className="font-sans text-lg font-medium text-red-700 dark:text-red-400">
                    Are you absolutely sure?
                  </AlertDialogTitle>
                  <AlertDialogDescription className="text-zinc-600 dark:text-zinc-400 text-sm pt-1">
                    This action cannot be undone. This will permanently delete
                    your account, running profiles, and recommendations. Please
                    type your password to confirm.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <form
                  onSubmit={(e) => {
                    e.preventDefault();
                    handleAccountDeletion();
                  }}
                  className="space-y-4 pt-2"
                >
                  {deleteError && (
                    <p className="text-red-600 dark:text-red-500 text-xs font-input bg-red-50 dark:bg-red-900/10 p-2 rounded-sm">
                      {deleteError}
                    </p>
                  )}
                  <ThemedInput
                    id="deletePassword"
                    label="Confirm Password"
                    type={showDeletePassword ? "text" : "password"}
                    placeholder=">********"
                    value={deletePassword}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                      setDeletePassword(e.target.value)
                    }
                    disabled={isDeleteLoading}
                    error={null}
                    showPasswordToggle={true}
                  >
                    <PasswordToggle
                      show={showDeletePassword}
                      setShow={setShowDeletePassword}
                      disabled={isDeleteLoading}
                    />
                  </ThemedInput>
                  <AlertDialogFooter className="pt-4">
                    <AlertDialogCancel
                      className="mt-0 border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-700/50 hover:bg-zinc-100 dark:hover:bg-zinc-700 hover:text-zinc-900 dark:hover:text-zinc-100 text-zinc-700 dark:text-zinc-300 text-xs h-8 px-3"
                      onClick={resetDeleteForm}
                    >
                      Cancel
                    </AlertDialogCancel>
                    <Button
                      type="submit"
                      variant="destructive"
                      className="bg-red-600 hover:bg-red-700 dark:bg-red-600 dark:hover:bg-red-500 text-white dark:text-white text-xs h-8 px-3 disabled:opacity-60"
                      disabled={isDeleteLoading || deletePassword.length === 0}
                    >
                      {isDeleteLoading ? (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      ) : (
                        <Trash2 className="mr-1.5 h-3.5 w-3.5" />
                      )}
                      {isDeleteLoading ? "Deleting..." : "Delete Permanently"}
                    </Button>
                  </AlertDialogFooter>
                </form>
              </AlertDialogContent>
            </AlertDialog>
          </CardContent>
        </Card>
      </div>

      {/* Sign Out Button */}
      <div className="flex justify-center pt-4">
        <Button
          variant="ghost"
          onClick={() => signOut()}
          className="text-zinc-500 dark:text-zinc-400 hover:text-red-600 dark:hover:text-red-400 hover:bg-red-100/70 dark:hover:bg-red-900/30 text-sm px-3 py-2 h-auto"
        >
          Sign Out
        </Button>
      </div>
    </div>
  );
}
