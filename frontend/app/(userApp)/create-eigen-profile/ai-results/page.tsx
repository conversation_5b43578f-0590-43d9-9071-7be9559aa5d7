"use client";

import React, {
  useState,
  useTran<PERSON><PERSON>,
  useEffect,
  use<PERSON><PERSON>back,
  useRef,
} from "react";
import { useRouter } from "next/navigation";
import {
  useRunningProfile,
  RunningProfileData,
} from "@/app/contexts/RunningProfileContext";
import { motion } from "framer-motion";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Input } from "@/components/ui/input";
import {
  Activity,
  BarChart3,
  Footprints,
  Info,
  Loader2,
  Ruler,
  Drum,
  ArrowRight,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>cle,
  XCircle,
  Send,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import { CollectedDataSummary } from "@/components/running-profile/CollectedDataSummary";
import {
  triggerProfileAnalysis,
  getRunningProfileData,
} from "@/actions/running-profile";

const randomArchTypes = ["low", "medium", "high"] as const;
const randomPronationTypes = [
  "neutral",
  "overpronation",
  "underpronation",
] as const;
const randomFootTypes = ["wide", "narrow", "normal"] as const;
const randomShoeSizes = [38, 39, 40, 41, 42, 43, 44, 45] as const;

const generateRandomAnalysisData = () => ({
  footLength: Math.floor(Math.random() * 10) + 22,
  footWidth: Math.floor(Math.random() * 5) + 8,
  archType: randomArchTypes[Math.floor(Math.random() * randomArchTypes.length)],
  pronationType:
    randomPronationTypes[
      Math.floor(Math.random() * randomPronationTypes.length)
    ],
  footType: randomFootTypes[Math.floor(Math.random() * randomFootTypes.length)],
  shoeSize: randomShoeSizes[Math.floor(Math.random() * randomShoeSizes.length)],
  stanceTime: Math.random() * 0.4 + 0.2,
  stepRate: Math.floor(Math.random() * 40) + 140,
  footStrike:
    Math.random() > 0.5 ? "midfoot" : Math.random() > 0.5 ? "forefoot" : "heel",
  verticalOscillation: Math.floor(Math.random() * 40) + 60,
});

enum AnalysisStatus {
  Idle,
  Triggering,
  Pending,
  Success,
  Error,
}

export default function AIResultsPage() {
  const {
    profileId,
    profileData: initialProfileData,
    steps,
    setCurrentStep,
    updateProfileData,
  } = useRunningProfile();
  const router = useRouter();
  const [isTriggeringPending, startTriggerTransition] = useTransition();
  const [isFetchingPending, startFetchingTransition] = useTransition();

  const [analysisStatus, setAnalysisStatus] = useState<AnalysisStatus>(
    AnalysisStatus.Idle
  );
  const [fetchedProfileData, setFetchedProfileData] =
    useState<RunningProfileData | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const currentStepIndex = steps.findIndex((step) =>
    step.href.includes("ai-results")
  );
  const nextStep = steps[currentStepIndex + 1];
  const prevStep = steps[currentStepIndex - 1];

  const handleTriggerAnalysis = () => {
    if (!profileId) {
      toast.error("Profile ID is missing.");
      setErrorMessage("Profile ID is missing. Cannot start analysis.");
      setAnalysisStatus(AnalysisStatus.Error);
      return;
    }
    setErrorMessage(null);
    setAnalysisStatus(AnalysisStatus.Triggering);

    startTriggerTransition(async () => {
      try {
        const result = await triggerProfileAnalysis(profileId);
        if (result.error) {
          throw new Error(result.error);
        }
        toast.success(result.message || "Analysis completed successfully!");

        // Get the updated profile data with analysis results
        const { profile } = await getRunningProfileData(profileId);
        if (profile) {
          // Update the context with the latest data
          updateProfileData(profile as RunningProfileData);

          // Redirect directly to the review page
          setCurrentStep(currentStepIndex + 1);
          router.push("/create-eigen-profile/review");
        } else {
          setAnalysisStatus(AnalysisStatus.Error);
          setErrorMessage("Failed to retrieve analysis results.");
        }
      } catch (err) {
        const message =
          err instanceof Error ? err.message : "Failed to trigger analysis.";
        toast.error(message);
        setErrorMessage(message);
        setAnalysisStatus(AnalysisStatus.Error);
      }
    });
  };

  const fetchData = useCallback(() => {
    if (!profileId || analysisStatus !== AnalysisStatus.Pending) return;

    console.log(
      "Attempting to fetch updated profile data via server action..."
    );
    startFetchingTransition(async () => {
      try {
        const { profile: fetchedProfile, error } = await getRunningProfileData(
          profileId
        );

        if (error) {
          console.error("Fetching profile data failed:", error);
          toast.error(`Failed to fetch results: ${error}`);

          return;
        }

        const profile = fetchedProfile as RunningProfileData | null;

        const hasGeneratedData =
          profile &&
          (profile.groundContactTime !== null ||
            profile.verticalOscillationVideo !== null ||
            profile.pelvicDrop !== null);

        if (hasGeneratedData) {
          console.log("Generated data found:", profile);
          setFetchedProfileData(profile);
          setAnalysisStatus(AnalysisStatus.Success);
          toast.success("Analysis results received!");
        } else {
          console.log(
            "Generated data not yet available in fetched profile.",
            profile
          );
        }
      } catch (err) {
        console.error("Error in fetchData transition:", err);
        const message =
          err instanceof Error
            ? err.message
            : "An unexpected error occurred fetching results.";
        toast.error(message);
      }
    });
  }, [profileId, analysisStatus, startFetchingTransition]);

  // Check for fake data on initial load and update context with latest data
  // Use a ref to track if we've already done the initial data fetch
  const initialDataFetchedRef = useRef(false);

  useEffect(() => {
    // If we have a profile ID, check if we already have analysis data
    // Only run this once on initial load
    if (
      profileId &&
      analysisStatus === AnalysisStatus.Idle &&
      !initialDataFetchedRef.current
    ) {
      initialDataFetchedRef.current = true; // Mark as fetched to prevent loops

      startFetchingTransition(async () => {
        try {
          const { profile, error } = await getRunningProfileData(profileId);

          if (error) {
            console.error("Initial profile data fetch failed:", error);
            return;
          }

          if (profile) {
            // Update the context with the latest data from the database
            console.log(
              "Updating context with latest profile data (one-time):",
              profile
            );
            updateProfileData(profile as RunningProfileData);

            // Check if we have any analysis data already
            const hasAnalysisData =
              profile.groundContactTime !== null ||
              profile.verticalOscillationVideo !== null ||
              profile.pelvicDrop !== null ||
              profile.posteriorStabilityScore !== null;

            if (hasAnalysisData) {
              console.log(
                "Analysis data already exists, redirecting to review page"
              );
              // Redirect directly to review page
              setCurrentStep(currentStepIndex + 1);
              router.push("/create-eigen-profile/review");
              return;
            }

            // Check if we're using fake data (placeholder URLs)
            const hasFakeData =
              profile.runningVideoSagittalUrl?.includes("fake-running-video") ||
              profile.footImageTopLeftUrl?.includes("fake-foot-image");

            if (hasFakeData) {
              // Add a note that we're using AI-generated data
              toast.info("Using AI-generated data for analysis");
            }
          }
        } catch (err) {
          console.error("Error checking initial profile data:", err);
        }
      });
    }
  }, [
    profileId,
    analysisStatus,
    startFetchingTransition,
    updateProfileData,
    router,
    currentStepIndex,
    setCurrentStep,
  ]);

  useEffect(() => {
    let intervalId: NodeJS.Timeout | null = null;
    if (analysisStatus === AnalysisStatus.Pending) {
      fetchData();
      intervalId = setInterval(fetchData, 5000); // Check more frequently (5s)
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [analysisStatus, fetchData]);

  const handleNext = () => {
    // Always redirect to the review page after analysis
    setCurrentStep(currentStepIndex + 1);
    router.push("/create-eigen-profile/review");
  };

  const handleBack = () => {
    setCurrentStep(currentStepIndex - 1);
    router.push(prevStep?.href || "/");
  };

  const renderContent = () => {
    switch (analysisStatus) {
      case AnalysisStatus.Idle:
        return (
          <div className="space-y-6">
            <Alert>
              <Sparkles className="h-4 w-4" />
              <AlertTitle>Ready for Analysis</AlertTitle>
              <AlertDescription>
                Review your collected data below, then click the "Send for
                Analysis" button to start the AI analysis. This process may take
                a few minutes.
              </AlertDescription>
            </Alert>

            {/* Check if we're using fake data */}
            {(initialProfileData?.footImageTopLeftUrl?.includes(
              "fake-foot-image"
            ) ||
              initialProfileData?.runningVideoSagittalUrl?.includes(
                "fake-running-video"
              )) && (
              <Alert className="bg-amber-50 dark:bg-amber-950/30 border-amber-200 dark:border-amber-800">
                <Info className="h-4 w-4 text-amber-600 dark:text-amber-500" />
                <AlertTitle className="text-amber-800 dark:text-amber-500">
                  Using AI-Generated Data
                </AlertTitle>
                <AlertDescription className="text-amber-700 dark:text-amber-400 text-sm">
                  You've chosen to use AI-generated data for some parts of your
                  profile. The analysis will be based on this synthetic data.
                </AlertDescription>
              </Alert>
            )}

            <div className="border-t border-zinc-200 dark:border-zinc-700 pt-6">
              <h3 className="text-lg font-medium mb-4">Your Collected Data</h3>
              <p className="text-sm text-zinc-500 dark:text-zinc-400 mb-4">
                Review your data below. You can edit or retake any part before
                sending for analysis.
              </p>
              <CollectedDataSummary profileData={initialProfileData} />
            </div>
          </div>
        );
      case AnalysisStatus.Triggering:
        return (
          <div className="flex flex-col items-center gap-4 p-6 text-center">
            <Loader2 className="h-8 w-8 animate-spin text-brand-green" />
            <p className="text-brand-black/60 dark:text-muted-foreground">
              Initiating analysis request...
            </p>
          </div>
        );
      case AnalysisStatus.Pending:
        return (
          <div className="space-y-4 p-6 text-center">
            <div className="flex justify-center">
              <Loader2 className="h-8 w-8 animate-spin text-brand-green" />
            </div>
            <p className="text-sm text-brand-black/60 dark:text-muted-foreground">
              Analysis in progress...
            </p>
            <p className="text-sm text-brand-black/60 dark:text-muted-foreground">
              Results typically take 1-3 minutes. We'll update this page
              automatically.
            </p>
            <Skeleton className="h-4 w-3/4 mx-auto mt-4 bg-zinc-200 dark:bg-zinc-700" />
            <Skeleton className="h-4 w-1/2 mx-auto bg-zinc-200 dark:bg-zinc-700" />
            <Skeleton className="h-4 w-2/3 mx-auto bg-zinc-200 dark:bg-zinc-700" />
          </div>
        );
      case AnalysisStatus.Success:
        return (
          <div className="space-y-4">
            <Alert className="border-emerald-700/50 dark:border-green-500 bg-emerald-50 dark:bg-green-900/20 text-emerald-700 dark:text-green-300">
              <CheckCircle className="h-4 w-4 text-emerald-700 dark:text-green-500" />
              <AlertTitle className="font-medium text-emerald-800 dark:text-green-200">
                Analysis Complete!
              </AlertTitle>
            </Alert>
            <h3 className="text-lg font-semibold text-brand-black dark:text-text-main">
              Generated Metrics:
            </h3>
            {fetchedProfileData ? (
              <ul className="list-disc pl-5 space-y-1 text-sm text-brand-black/70 dark:text-muted-foreground">
                {/* Display data using fetchedProfileData state variable */}
                <li className="font-semibold text-brand-black dark:text-text-main">
                  Foot Measurements:
                </li>
                {fetchedProfileData.heelToToeLengthLeft !== null && (
                  <li className="ml-4">
                    Foot Length (Left):{" "}
                    {(fetchedProfileData.heelToToeLengthLeft! / 10).toFixed(1)}{" "}
                    cm
                  </li>
                )}
                {fetchedProfileData.heelToToeLengthRight !== null && (
                  <li className="ml-4">
                    Foot Length (Right):{" "}
                    {(fetchedProfileData.heelToToeLengthRight! / 10).toFixed(1)}{" "}
                    cm
                  </li>
                )}
                {fetchedProfileData.forefootWidthLeft !== null && (
                  <li className="ml-4">
                    Forefoot Width (Left):{" "}
                    {(fetchedProfileData.forefootWidthLeft! / 10).toFixed(1)} cm
                  </li>
                )}
                {fetchedProfileData.forefootWidthRight !== null && (
                  <li className="ml-4">
                    Forefoot Width (Right):{" "}
                    {(fetchedProfileData.forefootWidthRight! / 10).toFixed(1)}{" "}
                    cm
                  </li>
                )}
                {fetchedProfileData.medialArchHeightLeft !== null && (
                  <li className="ml-4">
                    Arch Height (Left):{" "}
                    {(fetchedProfileData.medialArchHeightLeft! / 10).toFixed(1)}{" "}
                    cm
                  </li>
                )}
                {fetchedProfileData.medialArchHeightRight !== null && (
                  <li className="ml-4">
                    Arch Height (Right):{" "}
                    {(fetchedProfileData.medialArchHeightRight! / 10).toFixed(
                      1
                    )}{" "}
                    cm
                  </li>
                )}

                <li className="font-semibold text-brand-black dark:text-text-main mt-2">
                  Running Mechanics:
                </li>
                {fetchedProfileData.groundContactTime !== null && (
                  <li className="ml-4">
                    Ground Contact Time: {fetchedProfileData.groundContactTime}{" "}
                    ms
                  </li>
                )}
                {fetchedProfileData.verticalOscillationVideo !== null && (
                  <li className="ml-4">
                    Vertical Oscillation:{" "}
                    {fetchedProfileData.verticalOscillationVideo!.toFixed(1)} cm
                  </li>
                )}
                {fetchedProfileData.pelvicDrop !== null && (
                  <li className="ml-4">
                    Pelvic Drop: {fetchedProfileData.pelvicDrop!.toFixed(1)}°
                    {fetchedProfileData.pelvicDrop! > 2
                      ? " (High)"
                      : fetchedProfileData.pelvicDrop! < -2
                      ? " (High)"
                      : " (Normal)"}
                  </li>
                )}
                {fetchedProfileData.kneeDrift !== null && (
                  <li className="ml-4">
                    Knee Drift: {fetchedProfileData.kneeDrift!.toFixed(1)} cm
                    {Math.abs(fetchedProfileData.kneeDrift!) > 1.5
                      ? " (High)"
                      : " (Normal)"}
                  </li>
                )}

                <li className="font-semibold text-brand-black dark:text-text-main mt-2">
                  Stability Metrics:
                </li>
                {fetchedProfileData.posteriorStabilityScore !== null && (
                  <li className="ml-4">
                    Stability Score:{" "}
                    {fetchedProfileData.posteriorStabilityScore!.toFixed(0)}/100
                    {fetchedProfileData.posteriorStabilityScore! > 80
                      ? " (Excellent)"
                      : fetchedProfileData.posteriorStabilityScore! > 70
                      ? " (Good)"
                      : " (Needs Improvement)"}
                  </li>
                )}
                {fetchedProfileData.overstride !== null && (
                  <li className="ml-4">
                    Overstride: {fetchedProfileData.overstride!.toFixed(1)} cm
                    {fetchedProfileData.overstride! > 1
                      ? " (High)"
                      : " (Normal)"}
                  </li>
                )}
                {fetchedProfileData.footDrift !== null && (
                  <li className="ml-4">
                    Foot Drift: {fetchedProfileData.footDrift!.toFixed(1)} cm
                    {Math.abs(fetchedProfileData.footDrift!) > 1.5
                      ? " (High)"
                      : " (Normal)"}
                  </li>
                )}

                <li className="font-semibold text-brand-black dark:text-text-main mt-2">
                  Ankle & Knee Metrics:
                </li>
                {fetchedProfileData.ankleDorsiflexion !== null && (
                  <li className="ml-4">
                    Ankle Dorsiflexion:{" "}
                    {fetchedProfileData.ankleDorsiflexion!.toFixed(1)}°
                  </li>
                )}
                {fetchedProfileData.anklePlantarflexion !== null && (
                  <li className="ml-4">
                    Ankle Plantarflexion:{" "}
                    {fetchedProfileData.anklePlantarflexion!.toFixed(1)}°
                  </li>
                )}
                {fetchedProfileData.kneeFlexionLoading !== null && (
                  <li className="ml-4">
                    Knee Flexion:{" "}
                    {fetchedProfileData.kneeFlexionLoading!.toFixed(1)}°
                  </li>
                )}
              </ul>
            ) : (
              <p className="text-sm text-brand-black/70 dark:text-muted-foreground">
                Could not load analysis details.
              </p>
            )}
            <Alert className="bg-zinc-100 dark:bg-sweetspot-black/40 border-brand-black/20 dark:border-text-main/20 text-brand-black/80 dark:text-text-main/80">
              <Info className="h-4 w-4 text-brand-black/60 dark:text-text-main/60" />
              <AlertTitle className="font-medium text-brand-black dark:text-text-main">
                Next Steps
              </AlertTitle>
              <AlertDescription className="text-sm text-brand-black/70 dark:text-muted-foreground">
                You can now proceed to review and save your profile with the
                analysis results.
              </AlertDescription>
            </Alert>
          </div>
        );
      case AnalysisStatus.Error:
        return (
          <Alert
            variant="destructive"
            className="bg-red-100 dark:bg-red-900/30 border-red-500/50 text-red-700 dark:text-red-400"
          >
            <XCircle className="h-4 w-4" />
            <AlertTitle>Analysis Failed</AlertTitle>
            <AlertDescription>
              {errorMessage || "An error occurred during analysis."}
            </AlertDescription>
          </Alert>
        );
    }
  };

  const isLoading = isTriggeringPending || isFetchingPending;

  return (
    <div className="space-y-6 pb-12">
      {/* Page Title */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl sm:text-3xl font-sans font-semibold text-foreground">
          AI Analysis Results
        </h1>
      </div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="space-y-6"
      >
        <Card className="border-border/50 overflow-hidden">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 font-sans text-xl font-semibold text-foreground">
              <Sparkles className="h-5 w-5 text-primary" /> Analysis Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            {errorMessage && (
              <Alert
                variant="destructive"
                className="mb-4 bg-red-100 dark:bg-red-900/30 border-red-500/50 text-red-700 dark:text-red-400"
              >
                <XCircle className="h-4 w-4" />
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>{errorMessage}</AlertDescription>
              </Alert>
            )}
            {renderContent()}
          </CardContent>
          <CardFooter className="flex justify-between border-t border-border pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleBack}
              disabled={
                isLoading ||
                analysisStatus === AnalysisStatus.Pending ||
                analysisStatus === AnalysisStatus.Triggering
              }
              className="border-border hover:bg-secondary"
            >
              <ArrowLeft className="mr-2 h-4 w-4" /> Back
            </Button>

            {analysisStatus === AnalysisStatus.Idle && (
              <Button
                onClick={handleTriggerAnalysis}
                disabled={isLoading || !profileId}
                className="bg-primary text-primary-foreground hover:bg-primary/90"
              >
                {isLoading ? (
                  <span className="flex items-center justify-center gap-2">
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Starting Analysis...
                  </span>
                ) : (
                  <span className="flex items-center justify-center">
                    <Send className="mr-2 h-4 w-4" /> Send for Analysis
                  </span>
                )}
              </Button>
            )}

            {analysisStatus === AnalysisStatus.Success && (
              <Button
                onClick={handleNext}
                disabled={isLoading}
                className="bg-primary text-primary-foreground hover:bg-primary/90"
              >
                <span className="flex items-center justify-center">
                  Review & Save Profile <ArrowRight className="ml-2 h-4 w-4" />
                </span>
              </Button>
            )}
          </CardFooter>
        </Card>
      </motion.div>
    </div>
  );
}
