"use client";

import { useRunningProfile } from '@/app/contexts/RunningProfileContext';
import { UnifiedUploadStep } from '@/components/create-running-profile/UnifiedUploadStep';

export default function FootImageMedialLeftPage() {
  const { steps } = useRunningProfile();
  const currentStepIndex = steps.findIndex(step => step.href.includes('foot-imaging/medial-left'));
  const nextStep = steps[currentStepIndex + 1];
  const prevStep = steps[currentStepIndex - 1];

  return (
    <UnifiedUploadStep
      title="Foot Image: Medial Left"
      fieldName="footImageMedialLeftUrl"
      mediaType="image"
      nextStepHref={nextStep?.href || '/'}
      backStepHref={prevStep?.href || '/'}
      currentStepIndex={currentStepIndex}
    />
  );
}