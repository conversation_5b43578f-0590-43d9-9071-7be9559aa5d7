"use client";

import { useRunningProfile } from '@/app/contexts/RunningProfileContext';
import { UnifiedUploadStep } from '@/components/create-running-profile/UnifiedUploadStep';

export default function FootImageTopRightPage() {
  const { steps } = useRunningProfile();
  const currentStepIndex = steps.findIndex(step => step.href.includes('foot-imaging/top-right'));
  const nextStep = steps[currentStepIndex + 1];
  const prevStep = steps[currentStepIndex - 1];

  return (
    <UnifiedUploadStep
      title="Foot Image: Top Right"
      fieldName="footImageTopRightUrl"
      mediaType="image"
      nextStepHref={nextStep?.href || '/'}
      backStepHref={prevStep?.href || '/'}
      currentStepIndex={currentStepIndex}
    />
  );
}