"use client";

import { useRunningProfile } from '@/app/contexts/RunningProfileContext';
import { UnifiedUploadStep } from '@/components/create-running-profile/UnifiedUploadStep';

export default function FootImageMedialRightPage() {
  const { steps } = useRunningProfile();
  const currentStepIndex = steps.findIndex(step => step.href.includes('foot-imaging/medial-right'));
  const nextStep = steps[currentStepIndex + 1];
  const prevStep = steps[currentStepIndex - 1];

  return (
    <UnifiedUploadStep
      title="Foot Image: Medial Right"
      fieldName="footImageMedialRightUrl"
      mediaType="image"
      nextStepHref={nextStep?.href || '/'}
      backStepHref={prevStep?.href || '/'}
      currentStepIndex={currentStepIndex}
    />
  );
}