import type React from "react";
import { redirect } from "next/navigation";
import { getCurrentUser } from "@/lib/server-auth";
import { CreateProfileStepper } from "@/components/create-running-profile/CreateProfileStepper";
import { RunningProfileClientWrapper } from "./client-wrapper";

export default async function RunningProfileLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Get the current user with error handling
  let user;
  try {
    user = await getCurrentUser();

    // Redirect to sign in if not authenticated
    if (!user) {
      console.log("No authenticated user found, redirecting to signin");
      redirect("/signin");
    }
  } catch (error) {
    // Log the error and redirect to sign in
    console.error("Error in RunningProfileLayout:", error);
    redirect("/signin");
  }

  // If we get here, we have a valid user
  return (
    <div className="pb-20 sm:pb-4">
      <RunningProfileClientWrapper>
        <CreateProfileStepper />
        <main className="mt-2">
          {children}
        </main>
      </RunningProfileClientWrapper>
    </div>
  );
}
