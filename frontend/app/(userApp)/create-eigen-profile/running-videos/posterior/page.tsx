"use client";

import { useRunningProfile } from '@/app/contexts/RunningProfileContext';
import { UnifiedUploadStep } from '@/components/create-running-profile/UnifiedUploadStep';

export default function RunningVideoPosteriorPage() {
  const { steps } = useRunningProfile();
  const currentStepIndex = steps.findIndex(step => step.href.includes('running-videos/posterior'));
  const nextStep = steps[currentStepIndex + 1];
  const prevStep = steps[currentStepIndex - 1];

  return (
    <UnifiedUploadStep
      title="Running Video: Posterior"
      fieldName="runningVideoPosteriorUrl"
      mediaType="video"
      nextStepHref={nextStep?.href || '/'}
      backStepHref={prevStep?.href || '/'}
      currentStepIndex={currentStepIndex}
    />
  );
}