"use client";

import { useRunningProfile } from '@/app/contexts/RunningProfileContext';
import { UnifiedUploadStep } from '@/components/create-running-profile/UnifiedUploadStep';

export default function RunningVideoSagittalPage() {
  const { steps } = useRunningProfile();
  const currentStepIndex = steps.findIndex(step => step.href.includes('running-videos/sagittal'));
  const nextStep = steps[currentStepIndex + 1]; // Should be wearable-data step
  const prevStep = steps[currentStepIndex - 1];

  return (
    <UnifiedUploadStep
      title="Running Video: Sagittal"
      fieldName="runningVideoSagittalUrl"
      mediaType="video"
      nextStepHref={nextStep?.href || '/'}
      backStepHref={prevStep?.href || '/'}
      currentStepIndex={currentStepIndex}
    />
  );
}