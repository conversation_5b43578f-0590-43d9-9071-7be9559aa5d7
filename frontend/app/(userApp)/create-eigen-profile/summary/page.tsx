"use client";

import React, { useState, useMemo, useRef, useEffect, useTransition } from "react";
import { useRouter } from "next/navigation";
import { useRunningProfile } from "@/app/contexts/RunningProfileContext";
import {
  triggerProfileAnalysis,
  getRunningProfileData
} from "@/actions/running-profile";
import { motion } from "framer-motion";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  Check,
  Footprints,
  Loader2,
  ArrowLeft,
  Sparkles,
  Ruler,
  ChevronLeft,
  ChevronRight,
  UserCircle,
  Activity,
  Video,
  CalendarDays,
  Weight,
  Target,
  Mountain,
  Clock,
  ShieldAlert,
  Thermometer,
  Send,
} from "lucide-react";
import { ProfileDetailCard } from "@/components/profile/ProfileDetailCard";
import { StorageMedia } from "@/components/ui/storage-media";
import { toast } from "sonner";

// Analysis status enum
enum AnalysisStatus {
  Idle,
  Triggering,
  Pending,
  Success,
  Error,
}

// DataItem component for displaying profile data
interface DataItemProps {
  label: string;
  value: string | number | null | undefined;
  icon?: React.ElementType;
  unit?: string;
}

const DataItem = ({ label, value, icon: Icon, unit = "" }: DataItemProps) => (
  <div className="flex items-start space-x-2 p-2 bg-background hover:bg-muted/50 rounded-md transition-colors duration-150 min-h-[56px]">
    {Icon && <Icon className="h-4 w-4 mt-0.5 text-primary flex-shrink-0" />}
    <div className="flex-grow">
      <p className="text-xs font-medium text-muted-foreground">{label}</p>
      <p className="text-sm font-medium text-foreground">
        {value !== null && value !== undefined ? `${value}${unit}` : "N/A"}
      </p>
    </div>
  </div>
);

export default function SummaryPage() {
  const router = useRouter();
  const { profileData, updateProfileData, profileId, setCurrentStep, steps } = useRunningProfile();
  const sliderRef = useRef<HTMLDivElement>(null);
  const [isPending, startTransition] = useTransition();

  const [currentSlide, setCurrentSlide] = useState(0);
  const [isExpanded] = useState(true);
  const [analysisStatus, setAnalysisStatus] = useState<AnalysisStatus>(AnalysisStatus.Idle);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  // Calculate total number of cards (only input data cards)
  const totalCards = 3; // Runner Info, Foot Images, Running Videos

  // Navigation handlers for swipeable cards
  const handlePrevSlide = () => {
    if (currentSlide > 0) {
      setCurrentSlide(currentSlide - 1);
      if (sliderRef.current) {
        const cardWidth = sliderRef.current.querySelector("div")?.offsetWidth || 0;
        sliderRef.current.scrollTo({
          left: cardWidth * (currentSlide - 1),
          behavior: "smooth",
        });
      }
    }
  };

  const handleNextSlide = () => {
    if (currentSlide < totalCards - 1) {
      setCurrentSlide(currentSlide + 1);
      if (sliderRef.current) {
        const cardWidth = sliderRef.current.querySelector("div")?.offsetWidth || 0;
        sliderRef.current.scrollTo({
          left: cardWidth * (currentSlide + 1),
          behavior: "smooth",
        });
      }
    }
  };

  // Handle scroll events to update current slide
  useEffect(() => {
    const handleScroll = () => {
      if (sliderRef.current) {
        const scrollLeft = sliderRef.current.scrollLeft;
        const cardWidth = sliderRef.current.querySelector("div")?.offsetWidth || 0;
        if (cardWidth > 0) {
          const newSlide = Math.round(scrollLeft / cardWidth);
          if (newSlide !== currentSlide) {
            setCurrentSlide(newSlide);
          }
        }
      }
    };

    const sliderElement = sliderRef.current;
    if (sliderElement) {
      sliderElement.addEventListener("scroll", handleScroll);
      return () => {
        sliderElement.removeEventListener("scroll", handleScroll);
      };
    }
  }, [currentSlide]);

  // Handle analysis trigger
  const handleSendForAnalysis = async () => {
    if (!profileId) {
      toast.error("Profile ID not found");
      return;
    }

    setIsAnalyzing(true);
    setAnalysisStatus(AnalysisStatus.Triggering);

    startTransition(async () => {
      try {
        const result = await triggerProfileAnalysis(profileId);
        if (result.error) {
          throw new Error(result.error);
        }

        toast.success("Analysis started! This may take a few moments...");
        setAnalysisStatus(AnalysisStatus.Pending);

        // Poll for results
        const pollForResults = async () => {
          try {
            const { profile } = await getRunningProfileData(profileId);
            if (profile) {
              const hasNewAnalysisData =
                profile.groundContactTime !== null ||
                profile.verticalOscillationVideo !== null ||
                profile.pelvicDrop !== null ||
                profile.posteriorStabilityScore !== null;

              if (hasNewAnalysisData) {
                updateProfileData(profile);
                setAnalysisStatus(AnalysisStatus.Success);
                toast.success("Analysis completed successfully!");

                // Redirect to the running profile detail page after successful analysis
                setTimeout(() => {
                  router.push(`/running-profiles/${profileId}`);
                }, 2000);

                return true;
              }
            }
            return false;
          } catch (err) {
            console.error("Error polling for results:", err);
            return false;
          }
        };

        // Poll every 3 seconds for up to 2 minutes
        let attempts = 0;
        const maxAttempts = 40;
        const pollInterval = setInterval(async () => {
          attempts++;
          const completed = await pollForResults();

          if (completed || attempts >= maxAttempts) {
            clearInterval(pollInterval);
            setIsAnalyzing(false);

            if (!completed) {
              setAnalysisStatus(AnalysisStatus.Error);
              toast.error("Analysis is taking longer than expected. Please check back later.");
            }
          }
        }, 3000);

      } catch (error) {
        console.error("Error triggering analysis:", error);
        setAnalysisStatus(AnalysisStatus.Error);
        setIsAnalyzing(false);
        toast.error(error instanceof Error ? error.message : "Failed to start analysis");
      }
    });
  };

  const handleBack = () => {
    const currentStepIndex = steps.findIndex((step) =>
      step.href.includes("summary")
    );
    const prevStep = steps[currentStepIndex - 1];

    setCurrentStep(currentStepIndex - 1);
    router.push(prevStep?.href || "/");
  };

  return (
    <div className="space-y-6 pb-20 sm:pb-12">
      {/* Page Title */}
      <div className="text-center mb-6">
        <h1 className="text-2xl sm:text-3xl font-sans font-semibold text-foreground mb-2">
          Review Your Profile
        </h1>
        <p className="text-sm text-muted-foreground max-w-2xl mx-auto">
          Review all the information you've provided before sending for AI analysis
        </p>
      </div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="space-y-6"
      >
        {/* Card Titles and Navigation */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex flex-col">
            <h2 className="text-lg font-medium text-foreground">
              {currentSlide === 0 && "Personal Information"}
              {currentSlide === 1 && "Foot Images"}
              {currentSlide === 2 && "Running Videos"}
            </h2>
            <p className="text-xs text-muted-foreground">
              {currentSlide === 0 && "Your running background and physical details"}
              {currentSlide === 1 && "Top-down and side view foot photographs"}
              {currentSlide === 2 && "Sagittal and posterior running form videos"}
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handlePrevSlide}
              disabled={currentSlide === 0}
              className="h-8 w-8 rounded-full"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleNextSlide}
              disabled={currentSlide >= totalCards - 1}
              className="h-8 w-8 rounded-full"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Swipeable Cards Container */}
        <div
          ref={sliderRef}
          className="flex overflow-x-auto snap-x snap-mandatory scrollbar-hide pb-4 -mx-4 px-4 gap-4"
        >
          {/* Card 1: Runner Information */}
          <motion.div
            className="flex-shrink-0 w-full snap-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <ProfileDetailCard
              title="Runner Information"
              icon={<UserCircle className="h-5 w-5 mr-2 text-primary" />}
              isExpanded={isExpanded}
            >
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                <DataItem
                  label="Age"
                  value={profileData.age}
                  unit=" years"
                  icon={CalendarDays}
                />
                <DataItem
                  label="Sex/Gender"
                  value={profileData.gender}
                  icon={UserCircle}
                />
                <DataItem
                  label="Weight"
                  value={profileData.weightKg}
                  unit=" kg"
                  icon={Weight}
                />
                <DataItem
                  label="Height"
                  value={profileData.heightCm}
                  unit=" cm"
                  icon={Ruler}
                />
                <DataItem
                  label="Running Goal"
                  value={profileData.runningGoal}
                  icon={Target}
                />
                <DataItem
                  label="Primary Terrain"
                  value={profileData.terrain}
                  icon={Mountain}
                />
                <DataItem
                  label="Weekly Mileage"
                  value={profileData.averageWeeklyKm}
                  unit=" km"
                  icon={Activity}
                />
                <DataItem
                  label="Avg. Pace (Easy/Long)"
                  value={profileData.averagePaceEasyLong}
                  icon={Clock}
                />
                <DataItem
                  label="Climate"
                  value={profileData.climate}
                  icon={Thermometer}
                />
                <DataItem
                  label="Previous Injuries"
                  value={profileData.previousInjuries}
                  icon={ShieldAlert}
                />
              </div>
            </ProfileDetailCard>
          </motion.div>

          {/* Card 2: Foot Images */}
          <motion.div
            className="flex-shrink-0 w-full snap-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.05 }}
          >
            <ProfileDetailCard
              title="Foot Images"
              icon={<Footprints className="h-5 w-5 mr-2 text-primary" />}
              isExpanded={isExpanded}
            >
              <div className="space-y-4">
                {/* Foot Images */}
                {(profileData.footImageTopLeftUrl ||
                  profileData.footImageTopRightUrl ||
                  profileData.footImageMedialLeftUrl ||
                  profileData.footImageMedialRightUrl) ? (
                  <div className="grid grid-cols-2 gap-3">
                    {profileData.footImageTopLeftUrl && (
                      <div className="space-y-2">
                        <div className="aspect-square bg-black/5 rounded-lg overflow-hidden">
                          <StorageMedia
                            storagePath={profileData.footImageTopLeftUrl}
                            type="image"
                            aspectRatio="square"
                            className="w-full h-full object-contain"
                            showFakeDataLabel={false}
                          />
                        </div>
                        <p className="text-xs text-center text-muted-foreground">Left Foot (Top View)</p>
                      </div>
                    )}
                    {profileData.footImageTopRightUrl && (
                      <div className="space-y-2">
                        <div className="aspect-square bg-black/5 rounded-lg overflow-hidden">
                          <StorageMedia
                            storagePath={profileData.footImageTopRightUrl}
                            type="image"
                            aspectRatio="square"
                            className="w-full h-full object-contain"
                            showFakeDataLabel={false}
                          />
                        </div>
                        <p className="text-xs text-center text-muted-foreground">Right Foot (Top View)</p>
                      </div>
                    )}
                    {profileData.footImageMedialLeftUrl && (
                      <div className="space-y-2">
                        <div className="aspect-square bg-black/5 rounded-lg overflow-hidden">
                          <StorageMedia
                            storagePath={profileData.footImageMedialLeftUrl}
                            type="image"
                            aspectRatio="square"
                            className="w-full h-full object-contain"
                            showFakeDataLabel={false}
                          />
                        </div>
                        <p className="text-xs text-center text-muted-foreground">Left Foot (Side View)</p>
                      </div>
                    )}
                    {profileData.footImageMedialRightUrl && (
                      <div className="space-y-2">
                        <div className="aspect-square bg-black/5 rounded-lg overflow-hidden">
                          <StorageMedia
                            storagePath={profileData.footImageMedialRightUrl}
                            type="image"
                            aspectRatio="square"
                            className="w-full h-full object-contain"
                            showFakeDataLabel={false}
                          />
                        </div>
                        <p className="text-xs text-center text-muted-foreground">Right Foot (Side View)</p>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Footprints className="h-12 w-12 text-muted-foreground/40 mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">No foot images uploaded</p>
                  </div>
                )}
              </div>
            </ProfileDetailCard>
          </motion.div>

          {/* Card 3: Running Videos */}
          <motion.div
            className="flex-shrink-0 w-full snap-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
          >
            <ProfileDetailCard
              title="Running Videos"
              icon={<Video className="h-5 w-5 mr-2 text-primary" />}
              isExpanded={isExpanded}
            >
              <div className="space-y-4">
                {/* Videos */}
                {(profileData.runningVideoSagittalUrl || profileData.runningVideoPosteriorUrl) ? (
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    {profileData.runningVideoSagittalUrl && (
                      <div className="space-y-2">
                        <div className="aspect-video bg-black/5 rounded-lg overflow-hidden">
                          <StorageMedia
                            storagePath={profileData.runningVideoSagittalUrl}
                            type="video"
                            aspectRatio="video"
                            className="w-full h-full object-contain"
                            showFakeDataLabel={false}
                            loopPreview={false}
                          />
                        </div>
                        <p className="text-xs text-center text-muted-foreground">Sagittal View (Side)</p>
                      </div>
                    )}
                    {profileData.runningVideoPosteriorUrl && (
                      <div className="space-y-2">
                        <div className="aspect-video bg-black/5 rounded-lg overflow-hidden">
                          <StorageMedia
                            storagePath={profileData.runningVideoPosteriorUrl}
                            type="video"
                            aspectRatio="video"
                            className="w-full h-full object-contain"
                            showFakeDataLabel={false}
                            loopPreview={false}
                          />
                        </div>
                        <p className="text-xs text-center text-muted-foreground">Posterior View (Back)</p>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Video className="h-12 w-12 text-muted-foreground/40 mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">No running videos uploaded</p>
                  </div>
                )}
              </div>
            </ProfileDetailCard>
          </motion.div>


        </div>

        {/* Analysis Workflow Section */}
        <div className="space-y-6 mt-12">
          {!isAnalyzing && analysisStatus !== AnalysisStatus.Success && (
            <div className="text-center space-y-6">
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground max-w-md mx-auto">
                  Your profile will be sent to the Eigen Lab for biomechanical analysis using AI. This process typically takes 10 to 30 seconds.
                </p>
              </div>
              <Button
                onClick={handleSendForAnalysis}
                disabled={isAnalyzing || isPending}
                className="bg-primary text-primary-foreground hover:bg-primary/90 px-8 py-3 text-base"
                size="lg"
              >
                <span className="flex items-center justify-center gap-2">
                  <Sparkles className="h-5 w-5" />
                  Send for Analysis
                </span>
              </Button>
            </div>
          )}

          {isAnalyzing && (
            <div className="text-center space-y-6">
              <div className="flex flex-col items-center space-y-4">
                <div className="relative">
                  {/* Running animation */}
                  <div className="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center relative overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-transparent animate-pulse"></div>
                    <Footprints className="h-10 w-10 text-primary animate-bounce" />
                  </div>
                  {/* Running track animation */}
                  <div className="mt-4 w-32 h-1 bg-secondary rounded-full overflow-hidden">
                    <div className="h-full bg-primary rounded-full animate-pulse" style={{ width: "70%" }}></div>
                  </div>
                </div>
                <div className="text-center space-y-2">
                  <h3 className="text-xl font-semibold text-foreground">
                    Analyzing Your Running Profile
                  </h3>
                  <p className="text-sm text-muted-foreground max-w-md">
                    The Eigen Lab is analyzing your foot images and running videos using AI. This typically takes 10 to 30 seconds...
                  </p>
                </div>
              </div>
            </div>
          )}

          {analysisStatus === AnalysisStatus.Success && (
            <div className="text-center space-y-4">
              <div className="w-16 h-16 bg-emerald-100 dark:bg-emerald-950/30 rounded-full flex items-center justify-center mx-auto">
                <Check className="h-8 w-8 text-emerald-600 dark:text-emerald-500" />
              </div>
              <div className="space-y-2">
                <h3 className="text-xl font-semibold text-emerald-800 dark:text-emerald-500">
                  Analysis Complete!
                </h3>
                <p className="text-sm text-emerald-700 dark:text-emerald-400 max-w-md mx-auto">
                  Your running profile has been analyzed successfully. Redirecting to your complete profile...
                </p>
              </div>
            </div>
          )}

          {analysisStatus === AnalysisStatus.Error && (
            <div className="text-center space-y-4">
              <div className="w-16 h-16 bg-red-100 dark:bg-red-950/30 rounded-full flex items-center justify-center mx-auto">
                <ArrowLeft className="h-8 w-8 text-red-600 dark:text-red-500 rotate-180" />
              </div>
              <div className="space-y-2">
                <h3 className="text-xl font-semibold text-red-800 dark:text-red-500">
                  Analysis Error
                </h3>
                <p className="text-sm text-red-700 dark:text-red-400 max-w-md mx-auto">
                  There was an issue with the analysis. Please try again or contact support if the problem persists.
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Navigation Section */}
        {!isAnalyzing && analysisStatus !== AnalysisStatus.Success && (
          <div className="flex items-center justify-center pt-8">
            <Button
              type="button"
              variant="outline"
              onClick={handleBack}
              disabled={isPending}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Previous Step
            </Button>
          </div>
        )}
      </motion.div>
    </div>
  );
}