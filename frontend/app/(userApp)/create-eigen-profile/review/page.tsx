"use client";

import React, { useState, useMemo, useRef, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useRunningProfile } from "@/app/contexts/RunningProfileContext";
import {
  createOrUpdateRunningProfile,
  triggerProfileAnalysis,
  getRunningProfileData
} from "@/actions/running-profile";
import { motion } from "framer-motion";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  Check,
  Footprints,
  Loader2,
  Save,
  ArrowLeft,
  Sparkles,
  Ruler,
  ChevronLeft,
  ChevronRight,
  UserCircle,
  Activity,
  Video,
  CalendarDays,
  Weight,
  Target,
  Mountain,
  Clock,
  ShieldAlert,
  Thermometer,
  Send,
} from "lucide-react";
import { ProfileDetailCard } from "@/components/profile/ProfileDetailCard";
import { StorageMedia } from "@/components/ui/storage-media";
import { toast } from "sonner";

// Analysis status enum
enum AnalysisStatus {
  Idle,
  Triggering,
  Pending,
  Success,
  Error,
}

// DataItem component for displaying profile data
interface DataItemProps {
  label: string;
  value: string | number | null | undefined;
  icon?: React.ElementType;
  unit?: string;
}

const DataItem = ({ label, value, icon: Icon, unit = "" }: DataItemProps) => (
  <div className="flex items-start space-x-2 p-2 bg-background hover:bg-muted/50 rounded-md transition-colors duration-150 min-h-[56px]">
    {Icon && <Icon className="h-4 w-4 mt-0.5 text-primary flex-shrink-0" />}
    <div className="flex-grow">
      <p className="text-xs font-medium text-muted-foreground">{label}</p>
      <p className="text-sm font-medium text-foreground">
        {value !== null && value !== undefined ? `${value}${unit}` : "N/A"}
      </p>
    </div>
  </div>
);

export default function ReviewPage() {
  const router = useRouter();
  const { profileData, updateProfileData, profileId } = useRunningProfile();
  const sliderRef = useRef<HTMLDivElement>(null);

  // Generate a default profile name based on user data and running goal
  const defaultProfileName = useMemo(() => {
    const userName = profileData.name || "My Profile";
    const date = new Date().toLocaleDateString();
    const goal = profileData.runningGoal || "Running";
    return `${userName} - ${goal} - ${date}`;
  }, [profileData.name, profileData.runningGoal]);

  const [profileName, setProfileName] = useState(
    profileData.name || defaultProfileName
  );
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isExpanded] = useState(true);
  const [analysisStatus, setAnalysisStatus] = useState<AnalysisStatus>(AnalysisStatus.Idle);
  const [isAnalyzing, setIsAnalyzing] = useState(false);



  // Check if we have analysis data
  const hasAnalysisData = !!(
    profileData.kneeDrift !== null ||
    profileData.pelvicDrop !== null ||
    profileData.posteriorStabilityScore !== null ||
    profileData.groundContactTime !== null ||
    profileData.verticalOscillationVideo !== null
  );

  // Calculate total number of cards
  const totalCards = 3 + (hasAnalysisData ? 1 : 0); // Runner Info, Foot Images, Running Videos, + Analysis Results if available

  // Navigation handlers for swipeable cards
  const handlePrevSlide = () => {
    if (currentSlide > 0) {
      setCurrentSlide(currentSlide - 1);
      if (sliderRef.current) {
        const cardWidth = sliderRef.current.querySelector("div")?.offsetWidth || 0;
        sliderRef.current.scrollTo({
          left: cardWidth * (currentSlide - 1),
          behavior: "smooth",
        });
      }
    }
  };

  const handleNextSlide = () => {
    if (currentSlide < totalCards - 1) {
      setCurrentSlide(currentSlide + 1);
      if (sliderRef.current) {
        const cardWidth = sliderRef.current.querySelector("div")?.offsetWidth || 0;
        sliderRef.current.scrollTo({
          left: cardWidth * (currentSlide + 1),
          behavior: "smooth",
        });
      }
    }
  };

  // Handle scroll events to update current slide
  useEffect(() => {
    const handleScroll = () => {
      if (sliderRef.current) {
        const scrollLeft = sliderRef.current.scrollLeft;
        const cardWidth = sliderRef.current.querySelector("div")?.offsetWidth || 0;
        if (cardWidth > 0) {
          const newSlide = Math.round(scrollLeft / cardWidth);
          if (newSlide !== currentSlide) {
            setCurrentSlide(newSlide);
          }
        }
      }
    };

    const sliderElement = sliderRef.current;
    if (sliderElement) {
      sliderElement.addEventListener("scroll", handleScroll);
      return () => {
        sliderElement.removeEventListener("scroll", handleScroll);
      };
    }
  }, [currentSlide]);

  // Handle analysis trigger
  const handleSendForAnalysis = async () => {
    if (!profileId) {
      toast.error("Profile ID not found");
      return;
    }

    setIsAnalyzing(true);
    setAnalysisStatus(AnalysisStatus.Triggering);

    try {
      const result = await triggerProfileAnalysis(profileId);
      if (result.error) {
        throw new Error(result.error);
      }

      toast.success("Analysis started! This may take a few moments...");
      setAnalysisStatus(AnalysisStatus.Pending);

      // Poll for results
      const pollForResults = async () => {
        try {
          const { profile } = await getRunningProfileData(profileId);
          if (profile) {
            const hasNewAnalysisData =
              profile.groundContactTime !== null ||
              profile.verticalOscillationVideo !== null ||
              profile.pelvicDrop !== null ||
              profile.posteriorStabilityScore !== null;

            if (hasNewAnalysisData) {
              updateProfileData(profile);
              setAnalysisStatus(AnalysisStatus.Success);
              toast.success("Analysis completed successfully!");
              return true;
            }
          }
          return false;
        } catch (err) {
          console.error("Error polling for results:", err);
          return false;
        }
      };

      // Poll every 3 seconds for up to 2 minutes
      let attempts = 0;
      const maxAttempts = 40;
      const pollInterval = setInterval(async () => {
        attempts++;
        const completed = await pollForResults();

        if (completed || attempts >= maxAttempts) {
          clearInterval(pollInterval);
          setIsAnalyzing(false);

          if (!completed) {
            setAnalysisStatus(AnalysisStatus.Error);
            toast.error("Analysis is taking longer than expected. Please check back later.");
          }
        }
      }, 3000);

    } catch (error) {
      console.error("Error triggering analysis:", error);
      setAnalysisStatus(AnalysisStatus.Error);
      setIsAnalyzing(false);
      toast.error(error instanceof Error ? error.message : "Failed to start analysis");
    }
  };



  // Submit the profile
  const handleSubmit = async () => {
    if (!profileName.trim() || !profileData.id) return;

    setIsSubmitting(true);
    setError(null);

    try {
      // First, update the name in the database
      const result = await createOrUpdateRunningProfile(
        profileName,
        profileData.id
      );

      if (result.error) {
        throw new Error(result.error);
      }

      // Update the profile name and mark it as completed in the context
      updateProfileData({
        name: profileName,
        isCompleted: true, // Mark the profile as completed
      });

      // Mark success and prepare redirect
      setSuccess(true);

      // Redirect to the profile view page
      const profileViewHref = `/running-profiles/${profileData.id}`;
      console.log(
        "Redirecting to:",
        profileViewHref,
        "Profile ID:",
        profileData.id
      );

      setTimeout(() => {
        // Make sure we have a valid ID before redirecting
        if (profileData.id) {
          router.push(profileViewHref); // Redirect to profile view page
        } else {
          console.error("Missing profile ID for redirect");
          router.push("/running-profiles"); // Fallback to running profiles if ID is missing
        }
      }, 1500);
    } catch (error: unknown) {
      console.error("Error submitting profile:", error);
      setError(
        error instanceof Error
          ? error.message
          : "Failed to save your profile. Please try again."
      );
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6 pb-12">
      {/* Page Title */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl sm:text-3xl font-sans font-semibold text-foreground">
          Review & Analyze Profile
        </h1>
      </div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="space-y-6"
      >
        {/* Profile Name Input */}
        <Card className="border-border/50 overflow-hidden">
          <CardHeader>
            <CardTitle className="flex items-center font-sans text-xl font-semibold text-foreground">
              <Footprints className="h-5 w-5 mr-2 text-primary" />
              Profile Name
            </CardTitle>
            <CardDescription className="text-sm text-muted-foreground">
              Give your profile a memorable name
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 bg-secondary/30 p-4 rounded-lg border border-border">
              <Label
                htmlFor="profileName"
                className="text-sm font-medium text-foreground"
              >
                Profile Name
              </Label>
              <Input
                id="profileName"
                value={profileName}
                onChange={(e) => setProfileName(e.target.value)}
                placeholder="Enter a name for your profile"
                className="max-w-md border-border focus:border-primary text-base font-medium"
              />
              <p className="text-xs text-muted-foreground">
                This name will help you identify this profile in your dashboard.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Navigation Controls */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-muted-foreground">
              {currentSlide + 1} of {totalCards}
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handlePrevSlide}
              disabled={currentSlide === 0}
              className="h-8 w-8 rounded-full"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleNextSlide}
              disabled={currentSlide >= totalCards - 1}
              className="h-8 w-8 rounded-full"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Swipeable Cards Container */}
        <div
          ref={sliderRef}
          className="flex overflow-x-auto snap-x snap-mandatory scrollbar-hide pb-4 -mx-4 px-4 gap-4"
        >
          {/* Card 1: Runner Information */}
          <motion.div
            className="flex-shrink-0 w-full snap-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <ProfileDetailCard
              title="Runner Information"
              icon={<UserCircle className="h-5 w-5 mr-2 text-primary" />}
              isExpanded={isExpanded}
            >
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                <DataItem
                  label="Age"
                  value={profileData.age}
                  unit=" years"
                  icon={CalendarDays}
                />
                <DataItem
                  label="Sex/Gender"
                  value={profileData.gender}
                  icon={UserCircle}
                />
                <DataItem
                  label="Weight"
                  value={profileData.weightKg}
                  unit=" kg"
                  icon={Weight}
                />
                <DataItem
                  label="Height"
                  value={profileData.heightCm}
                  unit=" cm"
                  icon={Ruler}
                />
                <DataItem
                  label="Running Goal"
                  value={profileData.runningGoal}
                  icon={Target}
                />
                <DataItem
                  label="Primary Terrain"
                  value={profileData.terrain}
                  icon={Mountain}
                />
                <DataItem
                  label="Weekly Mileage"
                  value={profileData.averageWeeklyKm}
                  unit=" km"
                  icon={Activity}
                />
                <DataItem
                  label="Avg. Pace (Easy/Long)"
                  value={profileData.averagePaceEasyLong}
                  icon={Clock}
                />
                <DataItem
                  label="Climate"
                  value={profileData.climate}
                  icon={Thermometer}
                />
                <DataItem
                  label="Previous Injuries"
                  value={profileData.previousInjuries}
                  icon={ShieldAlert}
                />
              </div>
            </ProfileDetailCard>
          </motion.div>

          {/* Card 2: Foot Images */}
          <motion.div
            className="flex-shrink-0 w-full snap-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.05 }}
          >
            <ProfileDetailCard
              title="Foot Images"
              icon={<Footprints className="h-5 w-5 mr-2 text-primary" />}
              isExpanded={isExpanded}
            >
              <div className="space-y-4">
                {/* Foot Measurements */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  <DataItem
                    label="Left Foot Length"
                    value={profileData.heelToToeLengthLeft}
                    unit=" mm"
                    icon={Ruler}
                  />
                  <DataItem
                    label="Right Foot Length"
                    value={profileData.heelToToeLengthRight}
                    unit=" mm"
                    icon={Ruler}
                  />
                  <DataItem
                    label="Left Forefoot Width"
                    value={profileData.forefootWidthLeft}
                    unit=" mm"
                    icon={Ruler}
                  />
                  <DataItem
                    label="Right Forefoot Width"
                    value={profileData.forefootWidthRight}
                    unit=" mm"
                    icon={Ruler}
                  />
                </div>

                {/* Foot Images */}
                {(profileData.footImageTopLeftUrl ||
                  profileData.footImageTopRightUrl ||
                  profileData.footImageMedialLeftUrl ||
                  profileData.footImageMedialRightUrl) && (
                  <div>
                    <h4 className="text-xs font-medium text-muted-foreground mb-2">
                      Foot Images
                    </h4>
                    <div className="grid grid-cols-2 gap-2">
                      {profileData.footImageTopLeftUrl && (
                        <div className="aspect-square bg-black/5 rounded-lg overflow-hidden">
                          <StorageMedia
                            storagePath={profileData.footImageTopLeftUrl}
                            type="image"
                            aspectRatio="square"
                            className="w-full h-full object-contain"
                            showFakeDataLabel={false}
                          />
                          <p className="text-xs text-center mt-1">Left Top</p>
                        </div>
                      )}
                      {profileData.footImageTopRightUrl && (
                        <div className="aspect-square bg-black/5 rounded-lg overflow-hidden">
                          <StorageMedia
                            storagePath={profileData.footImageTopRightUrl}
                            type="image"
                            aspectRatio="square"
                            className="w-full h-full object-contain"
                            showFakeDataLabel={false}
                          />
                          <p className="text-xs text-center mt-1">Right Top</p>
                        </div>
                      )}
                      {profileData.footImageMedialLeftUrl && (
                        <div className="aspect-square bg-black/5 rounded-lg overflow-hidden">
                          <StorageMedia
                            storagePath={profileData.footImageMedialLeftUrl}
                            type="image"
                            aspectRatio="square"
                            className="w-full h-full object-contain"
                            showFakeDataLabel={false}
                          />
                          <p className="text-xs text-center mt-1">Left Side</p>
                        </div>
                      )}
                      {profileData.footImageMedialRightUrl && (
                        <div className="aspect-square bg-black/5 rounded-lg overflow-hidden">
                          <StorageMedia
                            storagePath={profileData.footImageMedialRightUrl}
                            type="image"
                            aspectRatio="square"
                            className="w-full h-full object-contain"
                            showFakeDataLabel={false}
                          />
                          <p className="text-xs text-center mt-1">Right Side</p>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </ProfileDetailCard>
          </motion.div>

          {/* Card 3: Running Videos */}
          <motion.div
            className="flex-shrink-0 w-full snap-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
          >
            <ProfileDetailCard
              title="Running Videos"
              icon={<Video className="h-5 w-5 mr-2 text-primary" />}
              isExpanded={isExpanded}
            >
              <div className="space-y-4">
                {/* Videos */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {profileData.runningVideoSagittalUrl && (
                    <div>
                      <h4 className="text-xs font-medium text-muted-foreground mb-2">
                        Sagittal View (Side)
                      </h4>
                      <div className="aspect-video bg-black/5 rounded-lg overflow-hidden">
                        <StorageMedia
                          storagePath={profileData.runningVideoSagittalUrl}
                          type="video"
                          aspectRatio="video"
                          className="w-full h-full object-contain"
                          showFakeDataLabel={false}
                          loopPreview={false}
                        />
                      </div>
                    </div>
                  )}
                  {profileData.runningVideoPosteriorUrl && (
                    <div>
                      <h4 className="text-xs font-medium text-muted-foreground mb-2">
                        Posterior View (Back)
                      </h4>
                      <div className="aspect-video bg-black/5 rounded-lg overflow-hidden">
                        <StorageMedia
                          storagePath={profileData.runningVideoPosteriorUrl}
                          type="video"
                          aspectRatio="video"
                          className="w-full h-full object-contain"
                          showFakeDataLabel={false}
                          loopPreview={false}
                        />
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </ProfileDetailCard>
          </motion.div>

          {/* Card 4: Analysis Results (if available) */}
          {hasAnalysisData && (
            <motion.div
              className="flex-shrink-0 w-full snap-center"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.15 }}
            >
              <ProfileDetailCard
                title="Analysis Results"
                icon={<Sparkles className="h-5 w-5 mr-2 text-primary" />}
                isExpanded={isExpanded}
              >
                <div className="space-y-4">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                    {profileData.posteriorStabilityScore !== null && (
                      <DataItem
                        label="Stability Score"
                        value={profileData.posteriorStabilityScore?.toFixed(0)}
                        unit="/100"
                        icon={Activity}
                      />
                    )}
                    {profileData.pelvicDrop !== null && (
                      <DataItem
                        label="Pelvic Drop"
                        value={profileData.pelvicDrop?.toFixed(1)}
                        unit="°"
                        icon={Activity}
                      />
                    )}
                    {profileData.kneeDrift !== null && (
                      <DataItem
                        label="Knee Drift"
                        value={profileData.kneeDrift?.toFixed(1)}
                        unit=" cm"
                        icon={Activity}
                      />
                    )}
                    {profileData.groundContactTime !== null && (
                      <DataItem
                        label="Ground Contact Time"
                        value={profileData.groundContactTime}
                        unit=" ms"
                        icon={Activity}
                      />
                    )}
                    {profileData.verticalOscillationVideo !== null && (
                      <DataItem
                        label="Vertical Oscillation"
                        value={profileData.verticalOscillationVideo?.toFixed(1)}
                        unit=" cm"
                        icon={Activity}
                      />
                    )}
                    {profileData.overstride !== null && (
                      <DataItem
                        label="Overstride"
                        value={profileData.overstride?.toFixed(1)}
                        unit=" cm"
                        icon={Activity}
                      />
                    )}
                  </div>
                </div>
              </ProfileDetailCard>
            </motion.div>
          )}
        </div>

        {/* Analysis and Save Actions */}
        <div className="space-y-4">
          {!hasAnalysisData && !isAnalyzing && analysisStatus !== AnalysisStatus.Success && (
            <Card className="border-border/50 overflow-hidden">
              <CardHeader>
                <CardTitle className="flex items-center font-sans text-xl font-semibold text-foreground">
                  <Sparkles className="h-5 w-5 mr-2 text-primary" />
                  Send for Analysis
                </CardTitle>
                <CardDescription className="text-sm text-muted-foreground">
                  Analyze your running form and foot biomechanics using AI
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col items-center space-y-4">
                  <p className="text-sm text-muted-foreground text-center">
                    Ready to analyze your running profile? Our AI will examine your foot images and running videos to provide personalized insights.
                  </p>
                  <Button
                    onClick={handleSendForAnalysis}
                    disabled={isAnalyzing}
                    className="bg-primary text-primary-foreground hover:bg-primary/90"
                    size="lg"
                  >
                    {isAnalyzing ? (
                      <span className="flex items-center justify-center gap-2">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Analyzing...
                      </span>
                    ) : (
                      <span className="flex items-center justify-center gap-2">
                        <Send className="h-4 w-4" />
                        Send for Analysis
                      </span>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {isAnalyzing && (
            <Card className="border-border/50 overflow-hidden">
              <CardContent className="p-6">
                <div className="flex flex-col items-center space-y-4">
                  <div className="relative">
                    {/* Placeholder for running shoe animation */}
                    <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
                      <Footprints className="h-8 w-8 text-primary animate-pulse" />
                    </div>
                  </div>
                  <div className="text-center">
                    <h3 className="text-lg font-medium text-foreground mb-2">
                      Analyzing Your Running Profile
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      Our AI is examining your foot images and running videos. This may take a few moments...
                    </p>
                  </div>
                  <div className="w-full max-w-xs">
                    <div className="h-2 bg-secondary rounded-full overflow-hidden">
                      <div className="h-full bg-primary rounded-full animate-pulse" style={{ width: "60%" }} />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {analysisStatus === AnalysisStatus.Success && (
            <Alert className="bg-emerald-50 dark:bg-emerald-950/30 border-emerald-200 dark:border-emerald-800">
              <Check className="h-4 w-4 text-emerald-600 dark:text-emerald-500" />
              <AlertTitle className="text-emerald-800 dark:text-emerald-500">
                Analysis Complete!
              </AlertTitle>
              <AlertDescription className="text-emerald-700 dark:text-emerald-400 text-sm">
                Your running profile has been analyzed successfully. You can now save your profile to view the complete results.
              </AlertDescription>
            </Alert>
          )}
        </div>

        {/* Save Profile Section */}
        <Card className="border-border/50 overflow-hidden">
          <CardHeader>
            <CardTitle className="flex items-center font-sans text-xl font-semibold text-foreground">
              <Save className="h-5 w-5 mr-2 text-primary" />
              Save Profile
            </CardTitle>
            <CardDescription className="text-sm text-muted-foreground">
              Save your profile to view the complete analysis and recommendations
            </CardDescription>
          </CardHeader>
          <CardFooter className="flex justify-center pt-6">
            <Button
              onClick={handleSubmit}
              disabled={isSubmitting || !profileName.trim() || success}
              className="w-full md:w-auto bg-primary text-primary-foreground hover:bg-primary/90"
              size="lg"
            >
              {isSubmitting ? (
                <span className="flex items-center justify-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Saving Profile...
                </span>
              ) : success ? (
                <span className="flex items-center justify-center gap-2">
                  <Check className="h-4 w-4" />
                  Profile Saved!
                </span>
              ) : (
                <span className="flex items-center justify-center gap-2">
                  <Save className="h-4 w-4" />
                  Save & View Profile
                </span>
              )}
            </Button>
          </CardFooter>
        </Card>

        {/* Error and Success Messages */}
        {error && (
          <Alert
            variant="destructive"
            className="bg-red-100 dark:bg-red-900/30 border-red-500/50 text-red-700 dark:text-red-400"
          >
            <AlertTitle className="font-sans font-medium">Error</AlertTitle>
            <AlertDescription className="font-input">{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="bg-emerald-50 dark:bg-emerald-950/30 border-emerald-200 dark:border-emerald-800">
            <Check className="h-4 w-4 text-emerald-600 dark:text-emerald-500" />
            <AlertTitle className="text-emerald-800 dark:text-emerald-500">Success!</AlertTitle>
            <AlertDescription className="text-emerald-700 dark:text-emerald-400">
              Your running profile has been saved. Redirecting to your profile overview...
            </AlertDescription>
          </Alert>
        )}

        {/* Back Button */}
        {!success && (
          <div className="mt-6">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
              disabled={isSubmitting}
              className="border-border hover:bg-secondary"
            >
              <ArrowLeft className="mr-2 h-4 w-4" /> Back
            </Button>
          </div>
        )}
      </motion.div>
    </div>
  );
}
