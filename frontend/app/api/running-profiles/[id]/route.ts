import { NextRequest, NextResponse } from "next/server";
import { PrismaClient, Prisma } from "@prisma/client";
import { createClient } from "@/utils/supabase/server";
import { z } from "zod";
import { getMediaUrlServer } from "@/lib/media-storage";

const prisma = new PrismaClient();

// Validation schema reflecting the NEW RunningProfile model for updates
// All fields are optional for partial updates via PUT/PATCH
const runningProfileSchema = z.object({
  // Profile info
  name: z.string().min(1, "Profile name cannot be empty").optional(), // Keep validation, but optional
  isDefault: z.boolean().optional(), // Keep

  // == Runner Profile Data ==
  age: z.number().int().optional().nullable(),
  heightCm: z.number().optional().nullable(),
  weightKg: z.number().optional().nullable(),
  gender: z.string().optional().nullable(),
  climate: z.string().optional().nullable(),
  terrain: z.string().optional().nullable(),
  previousInjuries: z.string().optional().nullable(), // String, not array
  averageWeeklyKm: z.string().optional().nullable(),
  averagePaceEasyLong: z.string().optional().nullable(),
  averageCadence: z.number().int().optional().nullable(),

  // == Foot Images (Storage Paths) ==
  footScanTopPhoto: z.string().optional().nullable(), // Storage path format: bucket:filePath
  footScanLeftPhoto: z.string().optional().nullable(), // Storage path format: bucket:filePath
  footScanRightPhoto: z.string().optional().nullable(), // Storage path format: bucket:filePath
  footScanBackPhoto: z.string().optional().nullable(), // Storage path format: bucket:filePath
  // Derived from images
  heelToToeLengthImage: z.number().optional().nullable(),
  forefootWidthImage: z.number().optional().nullable(),
  medialArchLengthImage: z.number().optional().nullable(),
  medialArchHeightImage: z.number().optional().nullable(),

  // == Running Videos (Storage Paths) ==
  runningVideoSide: z.string().optional().nullable(), // Storage path format: bucket:filePath
  runningVideoBack: z.string().optional().nullable(), // Storage path format: bucket:filePath
  // Derived from videos
  pelvicDrop: z.number().optional().nullable(),
  kneeDrift: z.number().optional().nullable(),
  footDriftCrossover: z.string().optional().nullable(),
  heelWhip: z.number().optional().nullable(),
  posteriorStabilityScore: z.number().optional().nullable(),
  overstride: z.number().optional().nullable(),
  ankleDorsiflexion: z.number().optional().nullable(),
  verticalOscillationVideo: z.number().optional().nullable(),
  trunkLean: z.number().optional().nullable(),
  kneeFlexionLoading: z.number().optional().nullable(),

  // == Heges Scan Data ==
  surfaceCurvatureMap: z.string().optional().nullable(),
  heelToToeLengthHeges: z.number().optional().nullable(),
  forefootWidthHeges: z.number().optional().nullable(),
  medialArchLengthHeges: z.number().optional().nullable(),
  medialArchHeightHeges: z.number().optional().nullable(),
  instepHeightHeges: z.number().optional().nullable(),

  // == Wearable Data ==
  runningPower: z.number().optional().nullable(),
  groundContactTime: z.number().optional().nullable(),
  verticalOscillationWearable: z.number().optional().nullable(),

  // Deprecated fields removed:
  // footScanVideo, gaitVideo, footLength, footWidth, archType, pronationType,
  // footScanNotes, runningFrequency, weeklyDistance, runningGoals, terrainTypes,
  // weight, height, shoeSize, currentShoes, currentShoeIssues, cushioningPreference,
  // weightPreference, supportPreference, dropPreference, colorPreference, importantFactors
});

/**
 * GET /api/running-profiles/[id]
 * Get a specific running profile
 */
export async function GET(
  _req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Validate required params
    const resolvedParams = await params;
    const profileId = resolvedParams.id;
    if (!profileId) {
      return NextResponse.json(
        { error: "Profile ID is required" },
        { status: 400 }
      );
    }

    // Get the current user from Supabase Auth
    const supabase = await createClient();
    const {
      data: { user: supabaseUser },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError || !supabaseUser) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user from database using Supabase user ID
    const user = await prisma.user.findUnique({
      where: { id: supabaseUser.id },
      include: {
        runningProfiles: true,
      },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Get running profile
    const runningProfile = await prisma.runningProfile.findUnique({
      where: { id: profileId },
      include: {
        recommendation: {
          include: {
            shoeModel1: true,
            shoeModel2: true,
            shoeModel3: true,
            shoeModel4: true,
            shoeModel5: true,
          },
        },
      },
    });

    if (!runningProfile) {
      return NextResponse.json(
        { error: "Running profile not found" },
        { status: 404 }
      );
    }

    // Check if profile belongs to user
    if (runningProfile.userId !== user.id) {
      return NextResponse.json(
        { error: "Unauthorized access to running profile" },
        { status: 403 }
      );
    }

    // Process media URLs to generate signed URLs for any storage paths
    const mediaFields = [
      "footImageTopLeftUrl",
      "footImageTopRightUrl",
      "footImageMedialLeftUrl",
      "footImageMedialRightUrl",
      "runningVideoPosteriorUrl",
      "runningVideoSagittalUrl",
      "footScanTopPhoto",
      "footScanLeftPhoto",
      "footScanRightPhoto",
      "footScanBackPhoto",
      "runningVideoSide",
      "runningVideoBack",
    ];

    // Create a copy of the profile to avoid modifying the original
    const profileWithSignedUrls = { ...runningProfile };

    // Generate signed URLs for all media fields
    for (const field of mediaFields) {
      const storagePath = runningProfile[field as keyof typeof runningProfile];

      if (
        storagePath &&
        typeof storagePath === "string" &&
        storagePath.includes(":")
      ) {
        try {
          const mediaUrl = await getMediaUrlServer(storagePath);

          if (mediaUrl) {
            // @ts-expect-error - We know these fields exist on the profile
            profileWithSignedUrls[field] = mediaUrl;
          } else {
            console.error(
              `Failed to generate media URL for ${field} with path ${storagePath}`
            );
          }
        } catch (error) {
          console.error(`Error generating signed URL for ${field}:`, error);
        }
      }
    }

    return NextResponse.json(
      { runningProfile: profileWithSignedUrls },
      { status: 200 }
    );
  } catch (error) {
    console.error("Running profile error:", error);
    return NextResponse.json(
      { error: "An error occurred while retrieving the running profile" },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/running-profiles/[id]
 * Update a specific running profile
 */
export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Validate required params
    const resolvedParams = await params;
    const profileId = resolvedParams.id;
    if (!profileId) {
      return NextResponse.json(
        { error: "Profile ID is required" },
        { status: 400 }
      );
    }

    // Get the current user from Supabase Auth
    const supabase = await createClient();
    const {
      data: { user: supabaseUser },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError || !supabaseUser) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await req.json();

    // Validate request body
    const result = runningProfileSchema.safeParse(body);
    if (!result.success) {
      return NextResponse.json(
        { error: "Invalid input", issues: result.error.issues },
        { status: 400 }
      );
    }

    // Get user from database using Supabase user ID
    const user = await prisma.user.findUnique({
      where: { id: supabaseUser.id },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Get running profile
    const existingProfile = await prisma.runningProfile.findUnique({
      where: { id: profileId },
    });

    if (!existingProfile) {
      return NextResponse.json(
        { error: "Running profile not found" },
        { status: 404 }
      );
    }

    // Check if profile belongs to user
    if (existingProfile.userId !== user.id) {
      return NextResponse.json(
        { error: "Unauthorized access to running profile" },
        { status: 403 }
      );
    }

    // Extract previousInjuries from the validated data
    const { previousInjuries, ...restData } = result.data;

    // Prepare data for update with correct Prisma type
    const updateData: Prisma.RunningProfileUpdateInput = { ...restData };

    // If setting this profile as default, unset any existing default
    if (updateData.isDefault === true) {
      await prisma.runningProfile.updateMany({
        where: {
          userId: user.id,
          isDefault: true,
          id: { not: profileId }, // Don't unset the one we are currently setting
        },
        data: {
          isDefault: false,
        },
      });
    }

    // Update profile with the validated data
    let runningProfile = await prisma.runningProfile.update({
      where: { id: profileId },
      data: updateData,
    });

    // Handle previousInjuries separately if it's defined
    if (previousInjuries !== undefined) {
      // Use a raw SQL query to update just the previousInjuries field
      await prisma.$executeRaw`
        UPDATE "RunningProfile"
        SET "previousInjuries" = ${
          previousInjuries === null ? null : previousInjuries
        }
        WHERE "id" = ${profileId}
      `;

      // Fetch the updated profile to get the correct data
      const updatedProfile = await prisma.runningProfile.findUnique({
        where: { id: profileId },
      });

      // Make sure we have a valid profile
      if (updatedProfile) {
        runningProfile = updatedProfile;
      }
    }

    // Process media URLs to generate signed URLs for any storage paths
    const mediaFields = [
      "footImageTopLeftUrl",
      "footImageTopRightUrl",
      "footImageMedialLeftUrl",
      "footImageMedialRightUrl",
      "runningVideoPosteriorUrl",
      "runningVideoSagittalUrl",
      "footScanTopPhoto",
      "footScanLeftPhoto",
      "footScanRightPhoto",
      "footScanBackPhoto",
      "runningVideoSide",
      "runningVideoBack",
    ];

    // Create a copy of the profile to avoid modifying the original
    const profileWithSignedUrls = { ...runningProfile };

    // Generate signed URLs for all media fields
    for (const field of mediaFields) {
      const storagePath = runningProfile[field as keyof typeof runningProfile];

      if (
        storagePath &&
        typeof storagePath === "string" &&
        storagePath.includes(":")
      ) {
        try {
          const mediaUrl = await getMediaUrlServer(storagePath);

          if (mediaUrl) {
            // @ts-expect-error - We know these fields exist on the profile
            profileWithSignedUrls[field] = mediaUrl;
          } else {
            console.error(
              `Failed to generate media URL for ${field} with path ${storagePath}`
            );
          }
        } catch (error) {
          console.error(`Error generating signed URL for ${field}:`, error);
        }
      }
    }

    return NextResponse.json(
      {
        message: "Running profile updated successfully",
        runningProfile: profileWithSignedUrls,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Running profile error:", error);
    return NextResponse.json(
      { error: "An error occurred while updating the running profile" },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/running-profiles/[id]
 * Delete a specific running profile
 */
export async function DELETE(
  _req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Validate required params
    const resolvedParams = await params;
    const profileId = resolvedParams.id;
    if (!profileId) {
      return NextResponse.json(
        { error: "Profile ID is required" },
        { status: 400 }
      );
    }

    // Get the current user from Supabase Auth
    const supabase = await createClient();
    const {
      data: { user: supabaseUser },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError || !supabaseUser) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user from database using Supabase user ID
    const user = await prisma.user.findUnique({
      where: { id: supabaseUser.id },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Get running profile
    const runningProfile = await prisma.runningProfile.findUnique({
      where: { id: profileId },
    });

    if (!runningProfile) {
      return NextResponse.json(
        { error: "Running profile not found" },
        { status: 404 }
      );
    }

    // Check if profile belongs to user
    if (runningProfile.userId !== user.id) {
      return NextResponse.json(
        { error: "Unauthorized access to running profile" },
        { status: 403 }
      );
    }

    // Delete the associated recommendation first
    await prisma.recommendation.deleteMany({
      where: { runningProfileId: profileId },
    });

    // Delete the profile
    await prisma.runningProfile.delete({
      where: { id: profileId },
    });

    return NextResponse.json(
      {
        message: "Running profile deleted successfully",
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Running profile error:", error);
    return NextResponse.json(
      { error: "An error occurred while deleting the running profile" },
      { status: 500 }
    );
  }
}
