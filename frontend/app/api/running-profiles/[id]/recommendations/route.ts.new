import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { createClient } from "@/utils/supabase/server";

const prisma = new PrismaClient();

/**
 * GET /api/running-profiles/[id]/recommendations
 * Generate recommendations for the specified running profile
 */
export async function GET(
  _req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Validate required params
    const resolvedParams = await params;
    const profileId = resolvedParams.id;
    if (!profileId) {
      return NextResponse.json(
        { error: "Profile ID is required" },
        { status: 400 }
      );
    }

    // Get the current user from Supabase Auth
    const supabase = await createClient();
    const {
      data: { user: supabaseUser },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError || !supabaseUser) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user ID from authenticated user
    const email = supabaseUser.email;
    if (!email) {
      return NextResponse.json(
        { error: "User email not found" },
        { status: 400 }
      );
    }

    // Get user from database
    const user = await prisma.user.findUnique({
      where: { email },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Get running profile
    const runningProfile = await prisma.runningProfile.findUnique({
      where: { id: profileId },
      include: {
        recommendation: {
          include: {
            shoeModel1: true,
            shoeModel2: true,
            shoeModel3: true,
            shoeModel4: true,
            shoeModel5: true,
          },
        },
      },
    });

    if (!runningProfile) {
      return NextResponse.json(
        { error: "Running profile not found" },
        { status: 404 }
      );
    }

    // Check if profile belongs to user
    if (runningProfile.userId !== user.id) {
      return NextResponse.json(
        { error: "Unauthorized access to running profile" },
        { status: 403 }
      );
    }

    // If we already have a recommendation, return it
    if (runningProfile.recommendation) {
      // Format the recommendation for the frontend
      const formattedRecommendation = {
        ...runningProfile.recommendation,
        // Add any additional formatting if needed
      };

      return NextResponse.json(
        {
          recommendation: formattedRecommendation,
        },
        { status: 200 }
      );
    }

    // Otherwise, generate new recommendations based on the profile
    // Build filter criteria based on running profile
    const filterCriteria: Record<string, any> = {}; // Use 'any' for flexibility here, or define a stricter type

    // --- Arch type mapping ---
    let derivedArchType: string | null = null;
    // Use the medial arch height from left foot (could average with right if needed)
    const archHeight = runningProfile.medialArchHeightLeft;
    if (archHeight !== null && archHeight !== undefined) {
      // Example thresholds (adjust as needed)
      if (archHeight < 1.0) {
        derivedArchType = "Flat feet"; // Example category in ShoeModel
      } else if (archHeight >= 1.0 && archHeight <= 2.5) {
        derivedArchType = "Neutral"; // Example category in ShoeModel
      } else if (archHeight > 2.5) {
        derivedArchType = "High arch"; // Example category in ShoeModel
      }

      if (derivedArchType) {
        filterCriteria.archType = derivedArchType; // Filter shoes that match this arch type
      }
    }

    // --- Pronation type mapping ---
    let derivedPronationType: string | null = null;
    // Use kneeDrift value to infer pronation
    // kneeDrift is a Float in the schema
    const kneeDriftValue = runningProfile.kneeDrift;
    if (kneeDriftValue !== null && kneeDriftValue !== undefined) {
      // Negative values indicate valgus (inward/overpronation)
      // Positive values indicate varus (outward/underpronation)
      const driftValue = Number(kneeDriftValue);
      if (!isNaN(driftValue)) {
        if (driftValue < -2) {
          derivedPronationType = "Overpronation"; // Example category in ShoeModel
        } else if (driftValue > 2) {
          derivedPronationType = "Underpronation"; // Example category in ShoeModel
        } else {
          derivedPronationType = "Neutral"; // Example category in ShoeModel
        }
      }

      if (derivedPronationType) {
        filterCriteria.pronation = derivedPronationType;
      }
    }

    // Terrain type
    if (runningProfile.terrain) {
      filterCriteria.terrain = runningProfile.terrain; // Match shoes suitable for this terrain
    }

    // Find matching shoe models
    const matchingShoeModels = await prisma.shoeModel.findMany({
      where: filterCriteria,
      take: 5, // Limit to top 5 matches
    });

    // If we don't have enough matching models, get some random ones to fill the slots
    if (matchingShoeModels.length < 5) {
      const additionalModels = await prisma.shoeModel.findMany({
        where: {
          id: {
            notIn: matchingShoeModels.map(model => model.id)
          }
        },
        take: 5 - matchingShoeModels.length
      });
      
      matchingShoeModels.push(...additionalModels);
    }

    // Create recommendation record with the top 5 shoe models
    const recommendationData: any = {
      runningProfileId: profileId,
      overallExplanation: "Based on your running profile, we've selected these shoes that match your needs.",
    };

    // Add shoe models to the recommendation
    matchingShoeModels.forEach((model, index) => {
      const position = index + 1;
      if (position <= 5) {
        recommendationData[`shoeModel${position}Id`] = model.id;
        
        // Generate explanation based on the model and profile
        let explanation = `${model.name} is a great match for your running style.`;
        
        // Add specific explanations based on derived data
        if (derivedArchType && model.archType === derivedArchType) {
          explanation += ` It provides support for your ${derivedArchType.toLowerCase()} arch type.`;
        }
        
        if (derivedPronationType && model.pronation === derivedPronationType) {
          explanation += ` It accommodates your ${derivedPronationType.toLowerCase()} running pattern.`;
        }
        
        if (runningProfile.terrain && model.terrain === runningProfile.terrain) {
          explanation += ` It's designed for ${runningProfile.terrain.toLowerCase()} running.`;
        }
        
        recommendationData[`explanation${position}`] = explanation;
      }
    });

    // Create the recommendation
    const newRecommendation = await prisma.recommendation.create({
      data: recommendationData,
      include: {
        shoeModel1: true,
        shoeModel2: true,
        shoeModel3: true,
        shoeModel4: true,
        shoeModel5: true,
      },
    });

    return NextResponse.json(
      {
        recommendation: newRecommendation,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Recommendations error:", error);
    return NextResponse.json(
      { error: "An error occurred while generating recommendations" },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/running-profiles/[id]/recommendations
 * Clear all recommendations for a profile to force regeneration
 */
export async function DELETE(
  _req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Validate required params
    const resolvedParams = await params;
    const profileId = resolvedParams.id;
    if (!profileId) {
      return NextResponse.json(
        { error: "Profile ID is required" },
        { status: 400 }
      );
    }

    // Get the current user from Supabase Auth
    const supabase = await createClient();
    const {
      data: { user: supabaseUser },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError || !supabaseUser) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    if (!supabaseUser.id) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Get running profile
    const runningProfile = await prisma.runningProfile.findUnique({
      where: { id: profileId },
    });

    if (!runningProfile) {
      return NextResponse.json(
        { error: "Running profile not found" },
        { status: 404 }
      );
    }

    // Check if profile belongs to user
    if (runningProfile.userId !== supabaseUser.id) {
      return NextResponse.json(
        { error: "Unauthorized access to running profile" },
        { status: 403 }
      );
    }

    // Delete the recommendation for this profile
    await prisma.recommendation.deleteMany({
      where: { runningProfileId: profileId },
    });

    return NextResponse.json(
      {
        message: "Recommendation cleared successfully",
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Recommendations error:", error);
    return NextResponse.json(
      { error: "An error occurred while clearing recommendations" },
      { status: 500 }
    );
  }
}
