import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { createClient } from "@/utils/supabase/server";

const prisma = new PrismaClient();

// Helper function to get the current user from Supabase Auth
async function getCurrentUser() {
  const supabase = await createClient();
  const {
    data: { user },
    error,
  } = await supabase.auth.getUser();

  if (error || !user) {
    return null;
  }

  return user;
}

/**
 * GET /api/running-profiles
 * Returns all running profiles for the current user
 */
export async function GET() {
  try {
    // Get the current user from Supabase Auth
    const supabaseUser = await getCurrentUser();

    if (!supabaseUser) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user from database using Supabase user ID
    const user = await prisma.user.findUnique({
      where: { id: supabaseUser.id },
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found in database" },
        { status: 404 }
      );
    }

    // Get all running profiles for this user
    const runningProfiles = await prisma.runningProfile.findMany({
      where: { userId: user.id },
      orderBy: { updatedAt: "desc" },
    });

    // Import the function to generate media URLs
    const { getMediaUrlServer } = await import("@/lib/media-storage");

    // Process media URLs to generate signed URLs for any storage paths
    const mediaFields = [
      "footImageTopLeftUrl",
      "footImageTopRightUrl",
      "footImageMedialLeftUrl",
      "footImageMedialRightUrl",
      "runningVideoPosteriorUrl",
      "runningVideoSagittalUrl",
      "footScanTopPhoto",
      "footScanLeftPhoto",
      "footScanRightPhoto",
      "footScanBackPhoto",
      "runningVideoSide",
      "runningVideoBack",
    ];

    // Process each profile to generate signed URLs
    const profilesWithSignedUrls = await Promise.all(
      runningProfiles.map(async (profile) => {
        // Create a copy of the profile to avoid modifying the original
        const profileWithSignedUrls = { ...profile };

        // Generate signed URLs for all media fields
        for (const field of mediaFields) {
          const storagePath = profile[field as keyof typeof profile];

          if (
            storagePath &&
            typeof storagePath === "string" &&
            storagePath.includes(":")
          ) {
            try {
              const mediaUrl = await getMediaUrlServer(storagePath);

              if (mediaUrl) {
                // @ts-expect-error - We know these fields exist on the profile
                profileWithSignedUrls[field] = mediaUrl;
              }
            } catch (error) {
              console.error(`Error generating signed URL for ${field}:`, error);
            }
          }
        }

        return profileWithSignedUrls;
      })
    );

    return NextResponse.json(
      {
        runningProfiles: profilesWithSignedUrls,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Running profiles error:", error);
    return NextResponse.json(
      { error: "An error occurred while retrieving running profiles" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/running-profiles
 * Creates a new running profile for the current user
 */
export async function POST(req: NextRequest) {
  try {
    // Get the current user from Supabase Auth
    const supabaseUser = await getCurrentUser();

    if (!supabaseUser) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await req.json();
    console.log("Profile data received:", JSON.stringify(body, null, 2));

    // Get user from database using Supabase user ID
    const user = await prisma.user.findUnique({
      where: { id: supabaseUser.id },
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found in database" },
        { status: 404 }
      );
    }

    // Validate required fields
    if (!body.name) {
      return NextResponse.json(
        { error: "Profile name is required" },
        { status: 400 }
      );
    }

    // Prepare profile data
    const profileData = {
      ...body,
      name: body.name,
      userId: user.id,
      // previousInjuries is now a string, not an array
    };

    // If this is the first profile, make it default
    const existingProfiles = await prisma.runningProfile.count({
      where: { userId: user.id },
    });

    const isDefault = existingProfiles === 0;

    try {
      // Create new profile
      const runningProfile = await prisma.runningProfile.create({
        data: {
          ...profileData,
          isDefault,
        },
      });

      console.log("Profile created successfully:", runningProfile.id);

      return NextResponse.json(
        {
          message: "Running profile created successfully",
          id: runningProfile.id,
          runningProfile,
        },
        { status: 201 }
      );
    } catch (dbError: unknown) {
      console.error("Database error creating profile:", dbError);
      return NextResponse.json(
        {
          error: `Database error: ${
            dbError instanceof Error ? dbError.message : "Unknown error"
          }`,
        },
        { status: 400 }
      );
    }
  } catch (error: unknown) {
    console.error("Running profile creation error:", error);
    return NextResponse.json(
      {
        error: `An error occurred while creating the running profile: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
      },
      { status: 500 }
    );
  }
}
