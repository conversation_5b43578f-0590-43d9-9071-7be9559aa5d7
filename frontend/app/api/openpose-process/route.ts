import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/server-auth";
import { getMediaUrlServer } from "@/lib/media-storage";

// Backend API URL
const BACKEND_API_URL = process.env.BACKEND_API_URL || "http://localhost:8000";

/**
 * POST /api/openpose-process
 * Processes a video with OpenPose by forwarding the request to the backend
 */
export async function POST(req: NextRequest) {
  try {
    // Get the current user
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get request body
    const body = await req.json();
    const { videoUrl, profileId, videoType } = body;

    if (!videoUrl || !profileId || !videoType) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Get a signed URL for the video if it's a storage path
    const signedVideoUrl = await getMediaUrlServer(videoUrl);
    if (!signedVideoUrl) {
      return NextResponse.json(
        { error: "Failed to generate signed URL for video" },
        { status: 500 }
      );
    }

    // Forward the request to the new backend endpoint
    const response = await fetch(`${BACKEND_API_URL}/analyze-url`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        video_url: signedVideoUrl,
        profile_id: profileId,
        video_type: videoType,
        user_id: user.id,
        visualize_results: true,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      return NextResponse.json(
        {
          error: `Backend error: ${errorData.error || response.statusText}`,
        },
        { status: response.status }
      );
    }

    // Return the response from the backend
    const data = await response.json();

    // Add success flag for frontend handling
    return NextResponse.json({
      ...data,
      profileId,
      videoType,
      analysisComplete: data.success,
    });
  } catch (error) {
    console.error("Error processing video with OpenPose:", error);
    return NextResponse.json(
      {
        error: `An error occurred while processing the video: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
      },
      { status: 500 }
    );
  }
}
