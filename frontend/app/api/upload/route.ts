import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/server-auth";
import {
  uploadToSupabaseS3,
  BUCKET_IMAGES,
  BUCKET_VIDEOS,
} from "@/lib/supabase-storage";
import sharp from "sharp";

export async function POST(req: NextRequest) {
  try {
    // Get the current user
    const user = await getCurrentUser();

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get form data
    const formData = await req.formData();
    const file = formData.get("file") as File | null;
    const profileId = formData.get("profileId") as string | null;
    const fieldToUpdate = formData.get("fieldToUpdate") as string | null;

    if (!file) {
      return NextResponse.json({ error: "No file provided" }, { status: 400 });
    }

    if (!profileId) {
      return NextResponse.json(
        { error: "No profile ID provided" },
        { status: 400 }
      );
    }

    if (!fieldToUpdate) {
      return NextResponse.json(
        { error: "No field to update provided" },
        { status: 400 }
      );
    }

    // Process the file
    let fileBuffer: Buffer | ArrayBuffer;
    let contentType: string;
    let fileName: string;
    const isImage = file.type.startsWith("image/");

    if (isImage) {
      // Process image with sharp
      const originalBuffer = Buffer.from(await file.arrayBuffer());
      fileBuffer = await sharp(originalBuffer).webp({ quality: 80 }).toBuffer();

      // Set content type and filename
      contentType = "image/webp";
      const originalNameWithoutExt = file.name
        .split(".")
        .slice(0, -1)
        .join(".");
      fileName = `${
        user.id
      }/${profileId}/${fieldToUpdate}-${Date.now()}-${originalNameWithoutExt}.webp`;
    } else {
      // For videos, use original buffer
      fileBuffer = await file.arrayBuffer();
      contentType = file.type;
      fileName = `${user.id}/${profileId}/${fieldToUpdate}-${Date.now()}-${
        file.name
      }`;
    }

    // Determine which bucket to use
    const bucket = isImage ? BUCKET_IMAGES : BUCKET_VIDEOS;

    // Upload to Supabase Storage using our utility function
    const uploadResult = await uploadToSupabaseS3(
      bucket,
      fileName,
      fileBuffer,
      contentType
    );

    // Format the storage path for database storage (bucket:filePath format)
    const storagePath = `${uploadResult.bucket}:${uploadResult.filePath}`;

    // Update the profile in the database using Prisma
    try {
      // Import the Prisma client
      const { prisma } = await import("@/lib/prisma");

      console.log(
        `Updating profile ${profileId} with field ${fieldToUpdate} = ${storagePath}`
      );

      // Use Prisma to update the profile with the storage path (not the signed URL which expires)
      await prisma.runningProfile.update({
        where: {
          id: profileId,
          userId: user.id,
        },
        data: {
          [fieldToUpdate]: storagePath,
        },
      });

      console.log(`Successfully updated profile ${profileId}`);
    } catch (dbError) {
      console.error("Database update error:", dbError);
      return NextResponse.json(
        {
          error: `Database error: ${
            dbError instanceof Error ? dbError.message : "Unknown error"
          }`,
        },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: "File uploaded successfully",
      fileUrl: uploadResult.signedUrl, // Return the signed URL for immediate use
      storagePath: storagePath, // Return the storage path for reference
    });
  } catch (error) {
    console.error("Upload error:", error);
    return NextResponse.json(
      { error: "An error occurred during file upload" },
      { status: 500 }
    );
  }
}
