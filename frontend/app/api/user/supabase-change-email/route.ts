import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { prisma } from "@/lib/db";

export async function POST(req: NextRequest) {
  try {
    // Get the current user from Supabase Auth
    const supabase = await createClient();
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Parse request body
    const body = await req.json();
    const { newEmail, password } = body;

    if (!newEmail || !password) {
      return NextResponse.json(
        { error: "New email and password are required" },
        { status: 400 }
      );
    }

    // Check if email is already in use by another user
    const existingUsers = await prisma.user.findMany({
      where: {
        email: newEmail,
        id: { not: user.id },
      },
    });

    const existingUser = existingUsers.length > 0 ? existingUsers[0] : null;

    if (existingUser && existingUser.id !== user.id) {
      return NextResponse.json(
        { error: "Email is already in use" },
        { status: 400 }
      );
    }

    // Update email in Supabase Auth
    const { error: updateError } = await supabase.auth.updateUser({
      email: newEmail,
      password: password, // Require password for security
    });

    if (updateError) {
      return NextResponse.json({ error: updateError.message }, { status: 400 });
    }

    // Update email in Prisma database
    await prisma.user.update({
      where: { id: user.id },
      data: { email: newEmail },
    });

    return NextResponse.json(
      { message: "Email updated successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Email change error:", error);
    return NextResponse.json(
      { error: "An error occurred while changing the email" },
      { status: 500 }
    );
  }
}
