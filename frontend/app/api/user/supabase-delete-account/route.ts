import { NextRequest, NextResponse } from "next/server";
import { createClient, createAdminClient } from "@/utils/supabase/server";
import { prisma } from "@/lib/db";

export async function POST(req: NextRequest) {
  try {
    // Get the current user from Supabase Auth
    const supabase = await createClient();
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Parse request body
    const body = await req.json();
    const { password } = body;

    if (!password) {
      return NextResponse.json(
        { error: "Password is required" },
        { status: 400 }
      );
    }

    // Verify the password
    const { error: signInError } = await supabase.auth.signInWithPassword({
      email: user.email!,
      password,
    });

    if (signInError) {
      return NextResponse.json({ error: "Invalid password" }, { status: 400 });
    }

    // First, delete the user in Supabase Auth using the admin client
    // This will trigger the handle_deleted_user trigger which will set deletedAt
    const adminClient = createAdminClient();
    const { error: deleteError } = await adminClient.auth.admin.deleteUser(
      user.id
    );

    if (deleteError) {
      return NextResponse.json({ error: deleteError.message }, { status: 400 });
    }

    // Verify that the user was soft-deleted in the database
    const dbUser = await prisma.user.findUnique({
      where: { id: user.id },
    });

    if (!dbUser || !dbUser.deletedAt) {
      // If the trigger didn't work, manually set the deletedAt timestamp
      try {
        await prisma.user.update({
          where: { id: user.id },
          data: { deletedAt: new Date() },
        });
      } catch (updateError) {
        console.error("Error setting deletedAt timestamp:", updateError);
        return NextResponse.json(
          { error: "Failed to soft-delete user" },
          { status: 500 }
        );
      }
    }

    return NextResponse.json(
      { message: "Account deleted successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Account deletion error:", error);
    return NextResponse.json(
      { error: "An error occurred while deleting the account" },
      { status: 500 }
    );
  }
}
