import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { createClient } from "@/utils/supabase/server";
import { getCurrentUser } from "@/lib/server-auth";

// Helper function to get Supabase client
async function getSupabase() {
  return await createClient();
}

// Validation schema reflecting the updated RunningProfile model
const runningProfileSchema = z.object({
  // Profile info
  name: z.string().optional().default("My Running Profile"),

  // == Runner Profile Data ==
  age: z.number().int().optional().nullable(),
  heightCm: z.number().int().optional().nullable(),
  weightKg: z.number().int().optional().nullable(),
  gender: z.string().optional().nullable(),
  climate: z.string().optional().nullable(),
  terrain: z.string().optional().nullable(),
  previousInjuries: z.string().optional().nullable(),
  previousInjuriesSeverity: z.string().optional().nullable(),
  averageWeeklyKm: z.string().optional().nullable(),
  averagePaceEasyLong: z.string().optional().nullable(),
  averageCadence: z.number().int().optional().nullable(),

  // == Wearable Data ==
  runningPower: z.number().int().optional().nullable(),
  groundContactTime: z.number().int().optional().nullable(),
  verticalOscillationWearable: z.number().optional().nullable(),

  // == Foot Images (URLs) ==
  footImageTopLeftUrl: z.string().url().optional().nullable(),
  footImageTopRightUrl: z.string().url().optional().nullable(),
  footImageMedialLeftUrl: z.string().url().optional().nullable(),
  footImageMedialRightUrl: z.string().url().optional().nullable(),
  // Derived from images
  heelToToeLengthImage: z.number().optional().nullable(),
  forefootWidthImage: z.number().optional().nullable(),
  medialArchLengthImage: z.number().optional().nullable(),
  medialArchHeightImage: z.number().optional().nullable(),

  // == Running Videos (URLs) ==
  runningVideoPosteriorUrl: z.string().url().optional().nullable(),
  runningVideoSagittalUrl: z.string().url().optional().nullable(),
  runningVideoPosteriorTimestamp: z.number().optional().nullable(),
  runningVideoSagittalTimestamp: z.number().optional().nullable(),
  // Derived from videos
  pelvicDrop: z.number().optional().nullable(),
  kneeDrift: z.number().optional().nullable(),
  footDrift: z.number().optional().nullable(),
  heelWhip: z.number().optional().nullable(),
  posteriorStabilityScore: z.number().optional().nullable(),
  overstride: z.number().optional().nullable(),
  ankleDorsiflexion: z.number().optional().nullable(),
  anklePlantarflexion: z.number().optional().nullable(),
  verticalOscillationVideo: z.number().optional().nullable(),
  trunkLean: z.number().optional().nullable(),
  kneeFlexionLoading: z.number().optional().nullable(),
});

export async function POST(req: NextRequest) {
  try {
    // Get the current user
    const user = await getCurrentUser();

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await req.json();

    // Validate request body
    const result = runningProfileSchema.safeParse(body);
    if (!result.success) {
      return NextResponse.json(
        { error: "Invalid input", issues: result.error.issues },
        { status: 400 }
      );
    }

    const supabase = await getSupabase();

    // Check if this is the first profile for the user
    const { count: existingProfilesCount, error: countError } = await supabase
      .from("running_profiles")
      .select("*", { count: "exact", head: true })
      .eq("user_id", user.id);

    if (countError) {
      console.error("Error counting profiles:", countError);
      return NextResponse.json(
        { error: "An error occurred while checking existing profiles" },
        { status: 500 }
      );
    }

    // Clean up the data for database storage
    const cleanedData = {
      ...result.data,
    };

    // Ensure we have a valid name if not provided
    if (!cleanedData.name) {
      cleanedData.name = `New Profile ${new Date().toLocaleDateString()}`;
    }

    // Convert camelCase to snake_case for Supabase
    const snakeCaseData = Object.entries(cleanedData).reduce(
      (acc, [key, value]) => {
        const snakeKey = key.replace(
          /[A-Z]/g,
          (letter) => `_${letter.toLowerCase()}`
        );
        return { ...acc, [snakeKey]: value };
      },
      {}
    );

    // Always create a new profile
    const { data: runningProfile, error: insertError } = await supabase
      .from("running_profiles")
      .insert([
        {
          ...snakeCaseData,
          user_id: user.id,
          is_default: existingProfilesCount === 0, // Only make default if it's the first profile
        },
      ])
      .select()
      .single();

    if (insertError) {
      console.error("Error creating profile:", insertError);
      return NextResponse.json(
        { error: "An error occurred while saving the running profile" },
        { status: 500 }
      );
    }

    return NextResponse.json(
      {
        message: "Running profile saved successfully",
        runningProfile,
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Running profile error:", error);
    return NextResponse.json(
      { error: "An error occurred while saving the running profile" },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    // Get the current user
    const user = await getCurrentUser();

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const supabase = await getSupabase();

    // Get running profile
    const { data: runningProfile, error } = await supabase
      .from("running_profiles")
      .select("*")
      .eq("user_id", user.id)
      .order("created_at", { ascending: false })
      .limit(1)
      .single();

    if (error && error.code !== "PGRST116") {
      // PGRST116 is "no rows returned" error
      console.error("Error fetching profile:", error);
      return NextResponse.json(
        { error: "An error occurred while retrieving the running profile" },
        { status: 500 }
      );
    }

    if (!runningProfile) {
      return NextResponse.json(
        {
          message: "No running profile found for this user",
          runningProfile: null,
        },
        { status: 200 }
      );
    }

    // Convert snake_case to camelCase for frontend
    const camelCaseProfile = Object.entries(runningProfile).reduce(
      (acc, [key, value]) => {
        const camelKey = key.replace(/_([a-z])/g, (_, letter) =>
          letter.toUpperCase()
        );
        return { ...acc, [camelKey]: value };
      },
      {}
    );

    return NextResponse.json(
      {
        runningProfile: camelCaseProfile,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Running profile error:", error);
    return NextResponse.json(
      { error: "An error occurred while retrieving the running profile" },
      { status: 500 }
    );
  }
}
