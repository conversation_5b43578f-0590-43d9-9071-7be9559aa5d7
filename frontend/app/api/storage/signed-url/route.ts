import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { parseStoragePath } from "@/lib/media-storage";
import { createServiceClient } from "@/lib/supabase-storage";

/**
 * POST /api/storage/signed-url
 * Generate a signed URL for a storage path
 * Body: { storagePath: string, expiresIn?: number }
 */
export async function POST(req: NextRequest) {
  try {
    // Get the current user from Supabase Auth
    const supabase = await createClient();
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get request body
    const body = await req.json();
    const { storagePath, expiresIn = 3600 } = body;

    if (!storagePath) {
      return NextResponse.json(
        { error: "Storage path is required" },
        { status: 400 }
      );
    }

    // Parse the storage path
    const path = parseStoragePath(storagePath);
    if (!path) {
      return NextResponse.json(
        { error: "Invalid storage path format. Expected 'bucket:filePath'" },
        { status: 400 }
      );
    }

    // Use the service client to generate a signed URL
    const serviceClient = createServiceClient();
    const { data, error } = await serviceClient.storage
      .from(path.bucket)
      .createSignedUrl(path.filePath, expiresIn);

    if (error) {
      console.error("Error creating signed URL:", error);
      return NextResponse.json(
        { error: `Failed to generate signed URL: ${error.message}` },
        { status: 500 }
      );
    }

    return NextResponse.json({
      signedUrl: data.signedUrl,
      expiresAt: Date.now() + expiresIn * 1000,
    });
  } catch (error) {
    console.error("Error generating signed URL:", error);
    return NextResponse.json(
      {
        error: `An error occurred: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/storage/signed-urls
 * Generate signed URLs for multiple storage paths
 * Body: { storagePaths: string[], expiresIn?: number }
 */
export async function PUT(req: NextRequest) {
  try {
    // Get the current user from Supabase Auth
    const supabase = await createClient();
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get request body
    const body = await req.json();
    const { storagePaths, expiresIn = 3600 } = body;

    if (!storagePaths || !Array.isArray(storagePaths)) {
      return NextResponse.json(
        { error: "Storage paths array is required" },
        { status: 400 }
      );
    }

    // Use the service client to generate signed URLs
    const serviceClient = createServiceClient();
    const result: Record<string, string> = {};
    const errors: Record<string, string> = {};

    // Process each storage path
    for (const storagePath of storagePaths) {
      const path = parseStoragePath(storagePath);
      if (!path) {
        errors[storagePath] = "Invalid storage path format";
        continue;
      }

      try {
        const { data, error } = await serviceClient.storage
          .from(path.bucket)
          .createSignedUrl(path.filePath, expiresIn);

        if (error) {
          errors[storagePath] = error.message;
        } else if (data && data.signedUrl) {
          result[storagePath] = data.signedUrl;
        }
      } catch (error) {
        errors[storagePath] = error instanceof Error ? error.message : "Unknown error";
      }
    }

    return NextResponse.json({
      signedUrls: result,
      errors: Object.keys(errors).length > 0 ? errors : undefined,
      expiresAt: Date.now() + expiresIn * 1000,
    });
  } catch (error) {
    console.error("Error generating signed URLs:", error);
    return NextResponse.json(
      {
        error: `An error occurred: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
      },
      { status: 500 }
    );
  }
}
