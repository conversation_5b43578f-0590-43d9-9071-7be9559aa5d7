import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { createClient } from "@/utils/supabase/server";
import { z } from "zod";

const prisma = new PrismaClient();

// Validation schema for shoe model data
const shoeModelSchema = z.object({
  brand: z.string().min(1, "Brand is required"),
  name: z.string().min(1, "Model name is required"),
  fullName: z.string().min(1, "Full name is required"),
  modelYear: z.number().int().min(2000, "Invalid model year"),
  category: z.string().min(1, "Category is required"),
  type: z.string().min(1, "Type is required"),
  image: z.string().url("Invalid image URL"),
  price: z.number().positive("Price must be positive"),

  // Technical specifications
  heelStackHeight: z.number().positive().optional().nullable(),
  forefootStackHeight: z.number().positive().optional().nullable(),
  drop: z.number().optional().nullable(),
  weight: z.number().positive().optional().nullable(),

  // Performance characteristics
  cushioningLevel: z.string().min(1, "Cushioning level is required"),
  cushioningSoftness: z.number().optional().nullable(),
  stability: z.string().min(1, "Stability level is required"),
  flexibility: z.string().min(1, "Flexibility is required"),
  durability: z.string().min(1, "Durability is required"),
  breathability: z.number().int().min(1).max(5).optional().nullable(),

  // Usage recommendations
  bestFor: z.array(z.string()),
  terrainTypes: z.array(z.string()),
  archTypes: z.array(z.string()),
  pronationTypes: z.array(z.string()),

  // Additional details
  description: z.string().min(1, "Description is required"),
  pros: z.array(z.string()),
  cons: z.array(z.string()),

  // Ratings
  rating: z.number().min(0).max(5).optional().nullable(),
  numberOfReviews: z.number().int().optional(),
});

/**
 * GET /api/shoe-models
 * Returns all shoe models with optional filtering
 */
export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url);

    // Parse query parameters
    const brand = url.searchParams.get("brand");
    const category = url.searchParams.get("category");
    const type = url.searchParams.get("type");
    const cushioningLevel = url.searchParams.get("cushioningLevel");
    const stability = url.searchParams.get("stability");
    const minPrice = url.searchParams.get("minPrice");
    const maxPrice = url.searchParams.get("maxPrice");
    const archType = url.searchParams.get("archType");
    const pronationType = url.searchParams.get("pronationType");
    const limit = url.searchParams.get("limit");
    const offset = url.searchParams.get("offset");

    // Build filter object
    const filter: Record<string, unknown> = {};

    if (brand) filter.brand = brand;
    if (category) filter.category = category;
    if (type) filter.type = type;
    if (cushioningLevel) filter.cushioningLevel = cushioningLevel;
    if (stability) filter.stability = stability;
    if (archType) filter.archTypes = { has: archType };
    if (pronationType) filter.pronationTypes = { has: pronationType };

    // Handle price range
    if (minPrice || maxPrice) {
      const priceFilter: Record<string, number> = {};
      if (minPrice) priceFilter.gte = parseFloat(minPrice);
      if (maxPrice) priceFilter.lte = parseFloat(maxPrice);
      filter.price = priceFilter;
    }

    // Get shoe models with pagination
    const shoeModels = await prisma.shoeModel.findMany({
      where: filter,
      orderBy: {
        updatedAt: "desc",
      },
      // Apply pagination if specified
      ...(limit ? { take: parseInt(limit) } : {}),
      ...(offset ? { skip: parseInt(offset) } : {}),
    });

    // Get total count for pagination
    const totalCount = await prisma.shoeModel.count({
      where: filter,
    });

    return NextResponse.json(
      {
        totalCount,
        shoeModels,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Shoe models error:", error);
    return NextResponse.json(
      { error: "An error occurred while retrieving shoe models" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/shoe-models
 * Creates a new shoe model (admin only)
 */
export async function POST(req: NextRequest) {
  try {
    // Get the current user from Supabase Auth
    const supabase = await createClient();
    const {
      data: { session },
    } = await supabase.auth.getSession();

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // TODO: Add admin check here when admin functionality is implemented

    const body = await req.json();

    // Validate request body
    const result = shoeModelSchema.safeParse(body);
    if (!result.success) {
      return NextResponse.json(
        { error: "Invalid input", issues: result.error.issues },
        { status: 400 }
      );
    }

    // Check if shoe model already exists
    const existingShoeModel = await prisma.shoeModel.findUnique({
      where: {
        brand_name_modelYear: {
          brand: result.data.brand,
          name: result.data.name,
          modelYear: result.data.modelYear,
        },
      },
    });

    if (existingShoeModel) {
      return NextResponse.json(
        { error: "Shoe model already exists" },
        { status: 400 }
      );
    }

    // Create new shoe model
    const shoeModel = await prisma.shoeModel.create({
      data: result.data,
    });

    return NextResponse.json(
      {
        message: "Shoe model created successfully",
        shoeModel,
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Shoe model error:", error);
    return NextResponse.json(
      { error: "An error occurred while creating the shoe model" },
      { status: 500 }
    );
  }
}
