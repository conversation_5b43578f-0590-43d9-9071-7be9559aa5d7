import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { createClient } from "@/utils/supabase/server";

const prisma = new PrismaClient();

/**
 * GET /api/shoe-models/[id]
 * Returns a specific shoe model by ID
 */
export async function GET(
  _req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Validate required params
    const resolvedParams = await params;
    const shoeId = resolvedParams.id;
    if (!shoeId) {
      return NextResponse.json(
        { error: "Shoe ID is required" },
        { status: 400 }
      );
    }

    // Get the current user from Supabase Auth
    const supabase = await createClient();
    const {
      data: { user: supabaseUser },
    } = await supabase.auth.getUser();

    // Get shoe model
    const shoeModel = await prisma.shoeModel.findUnique({
      where: { id: shoeId },
    });

    if (!shoeModel) {
      return NextResponse.json(
        { error: "Shoe model not found" },
        { status: 404 }
      );
    }

    // Get profiles that recommend this shoe
    const recommendingProfiles = await prisma.recommendation.findMany({
      where: {
        OR: [
          { shoeModel1Id: shoeId },
          { shoeModel2Id: shoeId },
          { shoeModel3Id: shoeId },
          { shoeModel4Id: shoeId },
          { shoeModel5Id: shoeId },
        ],
      },
      select: {
        id: true,
        runningProfile: {
          select: {
            id: true,
            name: true,
            userId: true,
          },
        },
      },
    });

    // Filter to only include profiles belonging to the current user
    const userRecommendingProfiles = supabaseUser 
      ? recommendingProfiles.filter(
          (rec) => rec.runningProfile.userId === supabaseUser.id
        )
      : [];

    return NextResponse.json(
      {
        shoeModel,
        recommendedInProfiles: userRecommendingProfiles,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Shoe model error:", error);
    return NextResponse.json(
      { error: "An error occurred while retrieving the shoe model" },
      { status: 500 }
    );
  }
}
