"use client";

import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { KeyRound } from "lucide-react";
import Link from "next/link";
import { LandingHeader } from "@/components/landing-header";
import SignUpForm from "@/components/auth/SignUpForm";

export default function SignupPage() {
  return (
    // Apply landing page theme structure
    <div className="flex flex-col min-h-screen font-mono overflow-x-hidden bg-off-white text-brand-black dark:bg-brand-black dark:text-text-main relative">
      {/* Background pattern */}
      <div className="absolute inset-0 w-full h-full bg-grid-pattern-light dark:bg-grid-pattern bg-grid-size opacity-10 dark:opacity-10 animate-subtle-pulse pointer-events-none z-0"></div>

      <LandingHeader />

      {/* Main content area */}
      <main className="flex-1 z-10 flex items-center justify-center py-12 sm:py-16 md:py-20">
        <Card className="w-full max-w-md bg-white/80 dark:bg-sweetspot-black/80 border border-brand-black/20 dark:border-text-main/20 rounded-md shadow-xl backdrop-blur-sm font-input">
          <CardHeader className="space-y-2 text-center pt-6 pb-4">
            <div className="flex justify-center">
              {/* Use a more thematic icon */}
              <KeyRound className="h-8 w-8 text-brand-black/70 dark:text-text-main/70" />
            </div>
            <CardTitle className="text-2xl font-sans font-medium text-brand-black dark:text-text-main">
              {/* Create Account */}
            </CardTitle>
            <CardDescription className="text-brand-black/60 dark:text-text-main/60 text-sm">
              Join Eigen to compile your profile.
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-4 pb-6 px-6">
            <SignUpForm />
          </CardContent>
          <CardFooter className="flex justify-center pb-6">
            <p className="text-xs text-center text-brand-black/60 dark:text-text-main/60">
              Already have an account?{" "}
              <Link
                href="/signin"
                className="font-medium text-brand-black/80 dark:text-text-main/80 hover:text-brand-black dark:hover:text-text-main underline underline-offset-2 decoration-dotted hover:decoration-solid transition-colors"
              >
                Sign in
              </Link>
            </p>
          </CardFooter>
        </Card>
      </main>

      {/* Simplified Footer */}
      <footer className="relative p-6 sm:p-8 bg-zinc-100 dark:bg-terminal-black border-t border-brand-black/15 dark:border-text-main/15 z-10 mt-auto">
        <div className="max-w-7xl mx-auto text-center font-light text-xs sm:text-sm text-brand-black/50 dark:text-text-main/50 space-y-1 px-4 sm:px-6">
          <p>&copy; {new Date().getFullYear()} eigen by CrossPath Labs</p>
          <div className="pt-1 space-x-3">
            <Link
              href="/privacy"
              className="hover:text-brand-black/80 dark:hover:text-text-main/80 transition-colors"
            >
              Privacy
            </Link>
            <span>|</span>
            <Link
              href="/terms"
              className="hover:text-brand-black/80 dark:hover:text-text-main/80 transition-colors"
            >
              Terms
            </Link>
          </div>
        </div>
      </footer>
    </div>
  );
}
