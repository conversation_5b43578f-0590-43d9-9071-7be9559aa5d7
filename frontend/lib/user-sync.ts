import { PrismaClient } from "@prisma/client";
import { createServiceClient } from "./supabase";

const prisma = new PrismaClient();

/**
 * Syncs a user from Supabase Auth to the Prisma User model
 * Call this when a user signs up or when user data changes
 */
export async function syncUserToPrisma(supabaseUserId: string) {
  try {
    // Get user data from Supabase Auth
    const supabase = createServiceClient();
    const { data: userData, error } = await supabase.auth.admin.getUserById(
      supabaseUserId
    );

    if (error || !userData.user) {
      console.error("Error fetching user from Supabase:", error);
      return null;
    }

    const user = userData.user;

    // Check if user already exists in Prisma
    const existingUser = await prisma.user.findUnique({
      where: { id: user.id },
    });

    // We don't need to check for soft-deleted users anymore
    // Just create a new user with the new auth ID

    if (existingUser) {
      // If user exists but was soft-deleted, undelete them
      const updateData: any = {
        email: user.email!,
        name: user.user_metadata.name || user.email!.split("@")[0],
        emailVerified: user.email_confirmed_at
          ? new Date(user.email_confirmed_at)
          : null,
        updatedAt: new Date(),
      };

      // If user was previously soft-deleted, clear the deletedAt field
      if (existingUser.deletedAt) {
        updateData.deletedAt = null;
      }

      // Update existing user
      return await prisma.user.update({
        where: { id: user.id },
        data: updateData,
      });
    } else {
      // Create new user
      return await prisma.user.create({
        data: {
          id: user.id,
          email: user.email!,
          name: user.user_metadata.name || user.email!.split("@")[0],
          emailVerified: user.email_confirmed_at
            ? new Date(user.email_confirmed_at)
            : null,
          deletedAt: null, // Explicitly set to null for new users
        },
      });
    }
  } catch (error) {
    console.error("Error syncing user to Prisma:", error);
    return null;
  }
}
