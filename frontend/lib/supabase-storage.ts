import { createClient } from "@supabase/supabase-js";

// Bucket names
export const BUCKET_IMAGES = "soyrunningimages";
export const BUCKET_VIDEOS = "soyrunningvideos";

/**
 * Creates a Supabase client with service role key for server-side operations
 * This bypasses RLS policies and should ONLY be used server-side
 */
export const createServiceClient = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || "";
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || "";

  if (!supabaseUrl) {
    console.error("Missing Supabase URL in environment variables");
    throw new Error("Missing Supabase URL. Check your environment variables.");
  }

  if (!supabaseServiceKey) {
    console.error("Missing Supabase service role key in environment variables");
    throw new Error(
      "Missing Supabase service role key. Check your environment variables."
    );
  }

  // Log partial key for debugging (first 4 chars only)
  const keyPrefix = supabaseServiceKey.substring(0, 4);
  console.log(`[DEBUG] Creating service client with URL: ${supabaseUrl}`);
  console.log(`[DEBUG] Service key prefix: ${keyPrefix}***`);

  try {
    return createClient(supabaseUrl, supabaseServiceKey);
  } catch (error) {
    console.error("Error creating Supabase service client:", error);
    throw new Error(
      `Failed to create Supabase service client: ${
        error instanceof Error ? error.message : "Unknown error"
      }`
    );
  }
};

/**
 * Uploads a file to Supabase Storage using the Storage API
 * This is a server-side function that uses the service role key to bypass RLS policies
 *
 * @param bucket - The bucket name ('soyrunningimages' or 'soyrunningvideos')
 * @param path - The path within the bucket (e.g., 'userId/profileId/filename.jpg')
 * @param file - The file buffer to upload
 * @param contentType - The content type of the file
 * @param expiresIn - Optional expiration time for the signed URL in seconds (default: 3600 = 1 hour)
 * @returns Object containing the file path, bucket name, and a temporary signed URL for immediate use
 */
export const uploadToSupabaseS3 = async (
  bucket: string,
  path: string,
  file: Buffer | ArrayBuffer,
  contentType: string,
  expiresIn: number = 3600
): Promise<{ bucket: string; filePath: string; signedUrl: string }> => {
  try {
    // Create Supabase client with service role key
    const supabase = createServiceClient();

    // Convert ArrayBuffer to Buffer if needed
    const fileBuffer = file instanceof ArrayBuffer ? Buffer.from(file) : file;

    // Check if bucket exists, create if not
    try {
      const { data: buckets } = await supabase.storage.listBuckets();
      const bucketExists = buckets?.some((b) => b.name === bucket) || false;

      if (!bucketExists) {
        console.log(`Creating bucket: ${bucket}`);
        const { error: createError } = await supabase.storage.createBucket(
          bucket,
          {
            public: false, // Make bucket private
            fileSizeLimit: 50 * 1024 * 1024, // 50MB limit
            allowedMimeTypes:
              bucket === BUCKET_IMAGES
                ? ["image/jpeg", "image/png", "image/webp", "image/gif"]
                : [
                    "video/mp4",
                    "video/quicktime",
                    "video/x-msvideo",
                    "video/webm",
                  ],
          }
        );

        if (createError) {
          console.error(`Error creating bucket ${bucket}:`, createError);
          throw createError;
        }
      }
    } catch (bucketError) {
      console.error("Error checking/creating bucket:", bucketError);
      // Continue anyway, the bucket might already exist
    }

    // Upload file using Supabase Storage API
    console.log(`Uploading to bucket: ${bucket}, path: ${path}`);
    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(path, fileBuffer, {
        contentType,
        upsert: true,
      });

    if (error) {
      console.error("Storage upload error:", error);
      throw error;
    }

    // Generate a signed URL for immediate use
    const { data: signedUrlData, error: signedUrlError } =
      await supabase.storage.from(bucket).createSignedUrl(path, expiresIn);

    if (signedUrlError) {
      console.error("Error creating signed URL:", signedUrlError);
      throw signedUrlError;
    }

    if (!signedUrlData || !signedUrlData.signedUrl) {
      throw new Error("Failed to generate signed URL");
    }

    console.log("Upload successful to bucket:", bucket, "path:", path);
    console.log(
      "Signed URL generated, valid for",
      expiresIn,
      "seconds:",
      signedUrlData.signedUrl
    );

    return {
      bucket: bucket,
      filePath: path,
      signedUrl: signedUrlData.signedUrl,
    };
  } catch (error) {
    console.error("Error uploading to Supabase Storage:", error);
    throw error;
  }
};
