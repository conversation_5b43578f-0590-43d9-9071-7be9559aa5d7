import { createClient } from "@/utils/supabase/client";
import { createServiceClient } from "@/lib/supabase-storage";

// Cache for storing signed URLs to avoid repeated requests
// Key: original URL, Value: { signedUrl: string, expiresAt: number }
type SignedUrlCache = {
  [key: string]: {
    signedUrl: string;
    expiresAt: number;
  };
};

const signedUrlCache: SignedUrlCache = {};

/**
 * Extracts bucket name and file path from a Supabase storage URL
 * @param url The Supabase storage URL
 * @returns Object containing bucket and path, or null if URL doesn't match pattern
 */
export function extractBucketAndPath(
  url: string
): { bucket: string; path: string } | null {
  // Remove any query parameters from the URL
  const urlWithoutQuery = url.split("?")[0];

  // Match URLs like https://tmasnbdsrlaotjrpskak.supabase.co/storage/v1/object/public/soyrunningimages/path/to/file.jpg
  const publicMatch = urlWithoutQuery.match(
    /\/storage\/v1\/object\/public\/([^\/]+)\/(.+)/
  );
  if (publicMatch) {
    return {
      bucket: publicMatch[1],
      path: publicMatch[2],
    };
  }

  // Match URLs like https://tmasnbdsrlaotjrpskak.supabase.co/storage/v1/object/sign/soyrunningimages/path/to/file.jpg
  const signMatch = urlWithoutQuery.match(
    /\/storage\/v1\/object\/sign\/([^\/]+)\/(.+)/
  );
  if (signMatch) {
    return {
      bucket: signMatch[1],
      path: signMatch[2],
    };
  }

  // Match URLs like https://tmasnbdsrlaotjrpskak.supabase.co/storage/v1/object/authenticated/soyrunningimages/path/to/file.jpg
  const authMatch = urlWithoutQuery.match(
    /\/storage\/v1\/object\/authenticated\/([^\/]+)\/(.+)/
  );
  if (authMatch) {
    return {
      bucket: authMatch[1],
      path: authMatch[2],
    };
  }

  // Match already signed URLs that contain a token
  // Example: https://tmasnbdsrlaotjrpskak.supabase.co/storage/v1/object/sign/soyrunningimages/path/to/file.jpg?token=...
  const signedUrlMatch = url.match(
    /\/storage\/v1\/object\/sign\/([^\/]+)\/([^?]+)/
  );
  if (signedUrlMatch) {
    return {
      bucket: signedUrlMatch[1],
      path: signedUrlMatch[2],
    };
  }

  return null;
}

/**
 * Generates a signed URL for a Supabase storage file (client-side)
 * @param url The original Supabase storage URL
 * @param expiresIn Expiration time in seconds (default: 3600 = 1 hour)
 * @returns The signed URL or the original URL if signing fails
 */
export async function getSignedUrl(
  url: string,
  expiresIn: number = 3600
): Promise<string> {
  if (!url || !url.includes("supabase.co")) {
    return url; // Return original URL if it's not a Supabase URL
  }

  // If URL already contains a token parameter, it's already signed
  if (url.includes("token=")) {
    return url;
  }

  // Check cache first
  const now = Date.now();
  const cached = signedUrlCache[url];
  if (cached && cached.expiresAt > now) {
    return cached.signedUrl;
  }

  // Extract bucket and path from URL
  const extracted = extractBucketAndPath(url);
  if (!extracted) {
    console.error(`Could not extract bucket and path from URL: ${url}`);
    return url;
  }

  const { bucket, path } = extracted;

  try {
    // Create a Supabase client
    const supabase = createClient();

    // Generate a signed URL
    const { data, error } = await supabase.storage
      .from(bucket)
      .createSignedUrl(path, expiresIn);

    if (error) {
      console.error(`Error creating signed URL for ${url}:`, error);
      return url;
    }

    if (data && data.signedUrl) {
      // Cache the signed URL
      signedUrlCache[url] = {
        signedUrl: data.signedUrl,
        expiresAt: now + expiresIn * 1000 * 0.9, // Set expiry slightly earlier than actual expiry
      };
      return data.signedUrl;
    }
  } catch (error) {
    console.error(`Error processing URL ${url}:`, error);
  }

  return url;
}

/**
 * Generates signed URLs for multiple Supabase storage files (client-side)
 * @param urls Array of original Supabase storage URLs
 * @param expiresIn Expiration time in seconds (default: 3600 = 1 hour)
 * @returns Object mapping original URLs to signed URLs
 */
export async function getSignedUrls(
  urls: (string | null | undefined)[],
  expiresIn: number = 3600
): Promise<Record<string, string>> {
  const result: Record<string, string> = {};
  const validUrls = urls.filter(
    (url): url is string => !!url && url.includes("supabase.co")
  );

  for (const url of validUrls) {
    result[url] = await getSignedUrl(url, expiresIn);
  }

  return result;
}

/**
 * Generates a signed URL for a Supabase storage file (server-side)
 * @param url The original Supabase storage URL
 * @param expiresIn Expiration time in seconds (default: 3600 = 1 hour)
 * @returns The signed URL or the original URL if signing fails
 */
export async function getSignedUrlServer(
  url: string,
  expiresIn: number = 3600
): Promise<string> {
  if (!url || !url.includes("supabase.co")) {
    return url; // Return original URL if it's not a Supabase URL
  }

  // If URL already contains a token parameter, it's already signed
  if (url.includes("token=")) {
    return url;
  }

  // Extract bucket and path from URL
  const extracted = extractBucketAndPath(url);
  if (!extracted) {
    console.error(`Could not extract bucket and path from URL: ${url}`);
    return url;
  }

  const { bucket, path } = extracted;

  try {
    // Create a Supabase service client (with admin privileges)
    const supabase = createServiceClient();

    // Generate a signed URL
    const { data, error } = await supabase.storage
      .from(bucket)
      .createSignedUrl(path, expiresIn);

    if (error) {
      console.error(`Error creating signed URL for ${url}:`, error);
      return url;
    }

    if (data && data.signedUrl) {
      return data.signedUrl;
    }
  } catch (error) {
    console.error(`Error processing URL ${url}:`, error);
  }

  return url;
}

/**
 * Generates signed URLs for multiple Supabase storage files (server-side)
 * @param urls Array of original Supabase storage URLs
 * @param expiresIn Expiration time in seconds (default: 3600 = 1 hour)
 * @returns Object mapping original URLs to signed URLs
 */
export async function getSignedUrlsServer(
  urls: (string | null | undefined)[],
  expiresIn: number = 3600
): Promise<Record<string, string>> {
  const result: Record<string, string> = {};
  const validUrls = urls.filter(
    (url): url is string => !!url && url.includes("supabase.co")
  );

  for (const url of validUrls) {
    result[url] = await getSignedUrlServer(url, expiresIn);
  }

  return result;
}

/**
 * Determines if a URL is a valid media URL
 * @param url The URL to check
 * @returns Boolean indicating if the URL is valid
 */
export function isValidMediaUrl(url: string | null | undefined): boolean {
  return !!url && (url.startsWith("http") || url.startsWith("/"));
}

/**
 * Determines if a URL points to fake/placeholder data
 * @param url The URL to check
 * @returns Boolean indicating if the URL points to fake data
 */
export function isFakeMediaData(url: string | null | undefined): boolean {
  if (!url) return false;

  return (
    url.includes("fake-foot-image") ||
    url.includes("fake-running-video") ||
    url.includes("fake-data-placeholder") ||
    url.includes("placehold.co")
  );
}
