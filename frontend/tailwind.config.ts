import type { Config } from "tailwindcss";

const config = {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
    "*.{js,ts,jsx,tsx,mdx}",
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      fontFamily: {
        sans: ["var(--font-space-grotesk)", "sans-serif"],
        mono: ["var(--font-jetbrains-mono)", "monospace"],
        jetbrains: ["var(--font-jetbrains-mono)", "monospace"],
        input: ["var(--font-input-mono)", "monospace"],
        courier: ["var(--font-courier-prime)", "monospace"],
      },
      colors: {
        // Syntax Highlighting Colors (Dark Mode - Original)
        "syntax-keyword": "#61AFEF", // Soft Blue
        "syntax-string": "#D19A66", // Soft Orange
        "syntax-comment": "#6A9955", // Darker Soft Green for better contrast on dark
        "syntax-number": "#56B6C2", // Cyan/Teal
        "syntax-variable": "#F7F7F5", // Off-White
        "syntax-punctuation": "#ABB2BF", // Grey

        // Syntax Highlighting Colors (Light Mode)
        "syntax-keyword-light": "#005cc5", // Darker Blue
        "syntax-string-light": "#a31515", // Dark Red/Brown
        "syntax-comment-light": "#22863a", // Dark Green
        "syntax-number-light": "#098658", // Dark Teal
        "syntax-variable-light": "#24292e", // Near Black
        "syntax-punctuation-light": "#586069", // Dark Grey

        "brand-black": "#121212",
        "terminal-black": "#2D2D2D",
        "sweetspot-black": "#1a1b1e",
        "off-white": "#ECEBE7",
        "text-main": "#F7F7F5",
        "text-sage": "#cbd4c9",
        "text-inverted": "#121313",
        brand: {
          green: "#004225", // British Racing Green
          "green-light": "#006400", // Lighter shade for hover/active
        },
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
        "subtle-pulse": {
          "0%, 100%": { opacity: "0.05" },
          "50%": { opacity: "0.08" },
        },
        // Removed glitch animation
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        "subtle-pulse": "subtle-pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite",
        // Removed glitch-hover animation
      },
      backgroundImage: {
        "grid-pattern":
          "linear-gradient(to right, rgba(247, 247, 245, 0.03) 1px, transparent 1px), linear-gradient(to bottom, rgba(247, 247, 245, 0.03) 1px, transparent 1px)", // Dark grid
        "grid-pattern-light":
          "linear-gradient(to right, rgba(18, 19, 19, 0.04) 1px, transparent 1px), linear-gradient(to bottom, rgba(18, 19, 19, 0.04) 1px, transparent 1px)", // Light grid
        "scanline-pattern":
          "linear-gradient(to bottom, rgba(18, 18, 18, 0) 0%, rgba(18, 18, 18, 0.3) 50%, rgba(18, 18, 18, 0) 100%)",
      },
      backgroundSize: {
        "grid-size": "20px 20px",
        "scanline-size": "100% 3px",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
} satisfies Config;

export default config;
